plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.jetbrains.kotlin.android)
    alias(libs.plugins.ksp)
    alias(libs.plugins.kotlin.parcelize)
    alias(libs.plugins.dagger.hilt.plugin)
    alias(libs.plugins.compose.compiler)
    alias(libs.plugins.jetbrains.kotlin.serialization)
    alias(libs.plugins.firebase.appdistribution)
    alias(libs.plugins.google.services)
    alias(libs.plugins.firebase.crashlytics)
}


android {
    namespace 'com.amobilab.ezmath.ai'


    defaultConfig {
        applicationId "com.amobilab.ezmath.ai"
        compileSdk libs.versions.compileSdkVersion.get().toInteger()
        minSdk libs.versions.minSdkVersion.get().toInteger()
        targetSdk libs.versions.targetSdkVersion.get().toInteger()
        versionCode 21
        // build_release_notes
        def versionMinor = rootProject.file('build_version.txt').text.trim()
        versionName "1.${versionCode}.${versionMinor}"
        multiDexEnabled true

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        ksp {
            arg("room.schemaLocation", "$projectDir/src/room_db_schemas")
        }

        setProperty("archivesBaseName", "EzMath_" + versionName + "_" + new Date().format('dd_MM_yyyy'))
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    def debugKeystorePropertiesFile = rootProject.file("debug_keystore.properties")
    def debugKeystoreProperties = new Properties()
    debugKeystoreProperties.load(new FileInputStream(debugKeystorePropertiesFile))

    def secretFile = rootProject.file("../keystore/ez_math_secret.properties")
    def debugAppCheck = "abc"
    if (secretFile.exists()) {
        def secretProperties = new Properties()
        secretProperties.load(new FileInputStream(secretFile))
        debugAppCheck = secretProperties['debugAppCheck'] ?: "abc"
    }

    flavorDimensions = ['ezmath']
    productFlavors {
        Alpha {
            dimension "ezmath"

            signingConfigs {
                debug {
                    keyAlias debugKeystoreProperties['keyAlias']
                    keyPassword debugKeystoreProperties['keyPassword']
                    storeFile file(debugKeystoreProperties['storeFile'])
                    storePassword debugKeystoreProperties['storePassword']
                    buildConfigField "String", "DEBUG_APP_CHECK", "\"$debugAppCheck\""
                }
                release {
                    keyAlias debugKeystoreProperties['keyAlias']
                    keyPassword debugKeystoreProperties['keyPassword']
                    storeFile file(debugKeystoreProperties['storeFile'])
                    storePassword debugKeystoreProperties['storePassword']
                    buildConfigField "String", "DEBUG_APP_CHECK", "\"$debugAppCheck\""
                }
            }

            buildTypes {
                release {
                    signingConfig signingConfigs.release
                    minifyEnabled true
                    proguardFiles 'proguard-project.txt'
                    proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
                    applicationVariants.all { variant ->
                        variant.outputs.all {
                            def formattedDate = new Date().format('dd_MM_yyyy')
                            outputFileName = "EzMath_${variant.name}-${variant.versionName}_${formattedDate}.apk"
                        }
                    }
                }
            }
        }

        Dev {
            dimension "ezmath"

            signingConfigs {
                debug {
                    keyAlias debugKeystoreProperties['keyAlias']
                    keyPassword debugKeystoreProperties['keyPassword']
                    storeFile file(debugKeystoreProperties['storeFile'])
                    storePassword debugKeystoreProperties['storePassword']
                    buildConfigField "String", "DEBUG_APP_CHECK", "\"$debugAppCheck\""
                }
                release {
                    keyAlias debugKeystoreProperties['keyAlias']
                    keyPassword debugKeystoreProperties['keyPassword']
                    storeFile file(debugKeystoreProperties['storeFile'])
                    storePassword debugKeystoreProperties['storePassword']
                    buildConfigField "String", "DEBUG_APP_CHECK", "\"$debugAppCheck\""
                }
            }

            buildTypes {
                release {
                    signingConfig signingConfigs.release
                    minifyEnabled true
                    proguardFiles 'proguard-project.txt'
                    proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
                    applicationVariants.all { variant ->
                        variant.outputs.all {
                            def formattedDate = new Date().format('dd_MM_yyyy')
                            outputFileName = "EzMath_${variant.name}-${variant.versionName}_${formattedDate}.apk"
                        }
                    }
                }
            }
        }

        Product {
            dimension "ezmath"

            signingConfigs {
                debug {
                    keyAlias debugKeystoreProperties['keyAlias']
                    keyPassword debugKeystoreProperties['keyPassword']
                    storeFile file(debugKeystoreProperties['storeFile'])
                    storePassword debugKeystoreProperties['storePassword']
                    buildConfigField "String", "DEBUG_APP_CHECK", "\"$debugAppCheck\""
                }
                release {
                    def keystorePropertiesFile = rootProject.file("../keystore/ez_math.properties")
                    if (keystorePropertiesFile.exists()) {
                        def keystoreProperties = new Properties()
                        keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

                        keyAlias keystoreProperties['keyAlias']
                        keyPassword keystoreProperties['keyPassword']
                        storeFile file(keystoreProperties['storeFile'])
                        storePassword keystoreProperties['storePassword']
                    } else {
                        keyAlias debugKeystoreProperties['keyAlias']
                        keyPassword debugKeystoreProperties['keyPassword']
                        storeFile file(debugKeystoreProperties['storeFile'])
                        storePassword debugKeystoreProperties['storePassword']
                    }

                    buildConfigField "String", "DEBUG_APP_CHECK", "\"abc\""
                }
            }

            buildTypes {
                release {
                    signingConfig signingConfigs.release
                    minifyEnabled true
                    proguardFiles 'proguard-project.txt'
                    proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
                    applicationVariants.all { variant ->
                        variant.outputs.all {
                            def formattedDate = new Date().format('dd_MM_yyyy')
                            outputFileName = "EzMath_${variant.name}-${variant.versionName}_${formattedDate}.apk"
                        }
                    }
                }
            }
        }
    }

    compileOptions {
        sourceCompatibility libs.versions.javaVersion.get().toInteger()
        targetCompatibility libs.versions.javaVersion.get().toInteger()
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        freeCompilerArgs += "-opt-in=androidx.compose.material3.ExperimentalMaterial3Api"
        freeCompilerArgs += "-opt-in=androidx.compose.foundation.ExperimentalFoundationApi"
        freeCompilerArgs += "-opt-in=com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi"
    }

    bundle {
        language {
            enableSplit = false
        }
    }

    buildFeatures {
        buildConfig = true

        compose true
        viewBinding true

        //noinspection DataBindingWithoutKapt
        dataBinding true
    }

    composeOptions {
        kotlinCompilerExtensionVersion libs.versions.composeCompiler.get()
    }
}

dependencies {
    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.androidx.material
    implementation libs.androidx.lifecycle.runtime
    implementation libs.androidx.lifecycle.runtime.ktx
    implementation libs.androidx.lifecycle.service
    implementation libs.androidx.lifecycle.viewmodel.ktx
    implementation libs.androidx.lifecycle.viewmodel.savedstate
    implementation libs.androidx.lifecycle.runtime.compose.android

    implementation libs.androidx.multidex

    // Compose
    implementation platform(libs.androidx.compose.bom)
    androidTestImplementation platform(libs.androidx.compose.bom)
    implementation libs.androidx.compose.material3
    implementation libs.androidx.compose.material
    implementation libs.androidx.compose.ui.tooling.preview
    debugImplementation libs.androidx.compose.ui.tooling
    androidTestImplementation libs.androidx.compose.ui.test.junit4
    debugImplementation libs.androidx.compose.ui.test.manifest
    implementation libs.androidx.compose.material.icons.extended
    implementation libs.androidx.compose.material3.window.size
    implementation libs.androidx.compose.activity
    implementation libs.androidx.compose.lifecycle.viewmodel
    implementation libs.androidx.compose.runtime.livedata
    implementation libs.androidx.compose.foundation
    implementation libs.androidx.compose.navigation

    // Utils
//    implementation libs.blankj.utils
    implementation libs.androidx.work.runtime
    implementation libs.androidx.work.runtime.ktx
    implementation libs.permissionx
    implementation libs.coil
    implementation libs.coil.compose
//    implementation libs.coil.video
    implementation libs.gson
    implementation libs.glide.compose
    implementation libs.glide
    annotationProcessor libs.glide.compiler
    coreLibraryDesugaring libs.desugarJdkLibs

    // Media3 ExoPlayer for video playback
    implementation libs.androidx.media3.exoplayer
    implementation libs.androidx.media3.ui
    implementation libs.androidx.media3.common



    // CameraX
    implementation libs.androidx.camera.core
    implementation libs.androidx.camera.camera2
    implementation libs.androidx.camera.view
    implementation libs.androidx.camera.lifecycle

    //guava
    implementation libs.guava

    // Comm module
    implementation project(':amobi_common')
    implementation project(':amobi_compose')
    implementation project(':amobi_compose_theme')
    implementation project(':amobi_otp')
    implementation project(':amobi_rate_me')
    implementation project(':amobi_openai')

    // commonmark
    implementation libs.commonmark
    implementation(libs.commonmark.ext.autolink)
    implementation(libs.commonmark.ext.footnotes)
    implementation(libs.commonmark.ext.ins)
    implementation(libs.commonmark.ext.task.list.items)
    implementation(libs.commonmark.ext.gfm.strikethrough)
    implementation(libs.commonmark.ext.gfm.tables)
    implementation(libs.commonmark.ext.heading.anchor)
    implementation(libs.commonmark.ext.image.attributes)
    implementation(libs.commonmark)
    //hỗ trợ xin quyền
    implementation libs.accompanist.permissions

    // Dagger Hilt Compose
    implementation libs.dagger.hilt.android
    ksp libs.dagger.hilt.compiler
    implementation libs.dagger.hilt.navigation.compose

//    implementation libs.halilibo.compose.richtext.commonmark
    implementation libs.halilibo.compose.richtext.ui.material3

    implementation libs.kotlinx.serialization.json

    implementation libs.jtokkit

    //Joda time
    implementation libs.joda.time
    // firebase
    implementation platform(libs.firebase.bom)
    implementation libs.firebase.auth
    implementation libs.firebase.analytics
    implementation libs.firebase.firestore
    implementation libs.firebase.vertexai
    implementation libs.firebase.crashlytics
    implementation libs.firebase.installations
    implementation libs.firebase.storage
    implementation libs.firebase.appcheck.playintegrity
    implementation libs.firebase.appcheck.debug

    implementation libs.google.services.auth
    implementation libs.google.googleid

    implementation libs.billing.ktx


    // animation
    implementation libs.lottie

    // PhotoView
    implementation libs.photoview

    implementation libs.kotlinx.coroutines.core

    implementation libs.accompanist.systemuicontroller

    debugImplementation libs.leakcanary.android

    // Compose

    implementation(libs.compose.swipebox.multiplatform)

    implementation(libs.balloon.compose)

    // Room Database
    implementation libs.androidx.room.runtime
    implementation libs.androidx.room.ktx
    annotationProcessor libs.androidx.room.compiler
    ksp libs.androidx.room.compiler


    api("com.powersync:core:1.1.1")

    // Nếu sử dụng Supabase Connector
    implementation("com.powersync:connector-supabase:1.1.1")


}