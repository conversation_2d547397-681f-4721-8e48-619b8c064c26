package com.amobilab.ezmath.ai.presentation.navigation

import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import kotlinx.serialization.Serializable

@Serializable
sealed class ScreenRoutes(val route: String) {
    @Serializable
    data class OnboardingScreen(val data: String = "") : ScreenRoutes("OnboardingScreen")

    @Serializable
    data class HomeScreen(val data: String = "") : ScreenRoutes("HomeScreen")

    @Serializable
    data class ScanTab(val data: String = "") : ScreenRoutes("ScanTab")

    @Serializable
    data class AiChatTab(
        val idHistory: String = "",
        val urlBitmap: String = "",
        val ask: String = "",
        val mode: ChatQuestionMode = ChatQuestionMode.Math
    ) : ScreenRoutes("AiChatTab")

    @Serializable
    data class ChatScreen(
        val idHistory: String = "",
        val urlBitmap: String = "",
        val ask: String = "",
        val mode: ChatQuestionMode = ChatQuestionMode.Math
    ) : ScreenRoutes("ChatScreen")

    @Serializable
    data class HistoryTab(val data: String = "") : ScreenRoutes("HistoryTab")

    @Serializable
    data class SettingTab(val data: String = "") : ScreenRoutes("SettingTab")

    @Serializable
    data class ScanDetail(
        val scanMode: ChatQuestionMode = ChatQuestionMode.Math,
        val url: String = "",
        val isFromCamera: Boolean = false,
        val rectFLeft: Float = 0f,
        val rectFTop: Float = 0f,
        val rectFRight: Float = 0f,
        val rectFBottom: Float = 0f
    ) : ScreenRoutes("ScanDetail")

    @Serializable
    data class Calculator(val data: String = "") : ScreenRoutes("Calculator")

    @Serializable
    data class InAppPurchaseRoute(val data: String = "") : ScreenRoutes("InAppPurchaseRoute")

    @Serializable
    data class CoinHistory(val data: String = "") : ScreenRoutes("CoinHistory")

    @Serializable
    data class SignIn(val data: String = "") : ScreenRoutes("SignIn")

    @Serializable
    data class UserDeleteScreen(
        val userId: String? = null,
        val username: String? = null,
        val profilePictureUrl: String? = null,
        val phoneNumber: String? = null,
        val email: String? = null
    ) : ScreenRoutes("UserInfoScreen")

    @Serializable
    data class SetTheme(val data: String = "") : ScreenRoutes("SetTheme")

    @Serializable
    data class FeedbackSuggestion(val data: String = "") : ScreenRoutes("FeedbackSuggestion")

    @Serializable
    data class Literature(val data: String = "") : ScreenRoutes("Literature")

    @Serializable
    data class ResearchAndAnalysis(val data: String = "") : ScreenRoutes("ResearchAndAnalysis")

    @Serializable
    data class WriteAnEssay(val data: String = "") : ScreenRoutes("WriteAnEssay")

    @Serializable
    data class ScreenTestKey(val data: String = "") : ScreenRoutes("ScreenTestKey")
}