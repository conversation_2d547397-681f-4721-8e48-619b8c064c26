package com.amobilab.ezmath.ai.data.pref

import amobi.module.common.configs.CommFigs
import com.amobilab.ezmath.ai.presentation.common.shared_values.AppThemeMode
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode

object PrefConst {
    const val APP_THEME_MODE = "APP_THEME_MODE"

    const val TRANSLATE_TARGET = "TRANSLATE_TARGET"
    const val MODEL_AI = "MODEL_AI"
    const val SORT_BY = "SORT_BY"
    const val FIRST_TIME_OPEN_APP = "FIRST_TIME_OPEN_APP"
    const val FREE_CHAT = "FREE_CHAT"
    const val FREE_CHAT_SYNCHRONIZED = "FREE_CHAT_SYNCHRONIZED"
    const val ANDROID_ID = "ANDROID_ID"
    const val TOKEN_ID = "TOKEN_ID"
    const val FIREBASE_ID_TOKEN = "FIREBASE_ID_TOKEN"
    const val USER_ID = "USER_ID"
    const val SHOW_TOOLTIP_TAB_SCAN = "SHOW_TOOLTIP_TAB_SCAN"
    const val SHOW_TOOLTIP_TAB_CHAT_COMPOSE = "SHOW_TOOLTIP_TAB_CHAT_COMPOSE"
    const val ONBOARDING_COMPLETED = "ONBOARDING_COMPLETED"


    const val TOTAL_COIN_BALANCE = "TOTAL_COIN_BALANCE"

    object Token {
        const val INPUT_TOKEN_COUNT = "inputTokenCount"
        const val OUTPUT_TOKEN_COUNT = "outputTokenCount"
        const val TOTAL_TOKEN_COUNT = "totalTokenCount"
    }


    fun getDefString(key: String): String {
        return when (key) {
            APP_THEME_MODE -> AppThemeMode.SYSTEM.name
            TRANSLATE_TARGET -> "English (US)"
            MODEL_AI -> ModelAiMode.GEMINI.name
            else -> ""
        }
    }

    fun getDefLong(key: String): Long {
        return when (key) {
            else -> 0L
        }
    }


    fun getDefBoolean(key: String): Boolean {
        return when (key) {
            SHOW_TOOLTIP_TAB_SCAN -> true
            SHOW_TOOLTIP_TAB_CHAT_COMPOSE -> true
            else -> false
        }
    }

    fun getDefInt(key: String): Int {
        return when (key) {
            FREE_CHAT -> if (CommFigs.IS_PRODUCT) 10 else 3
            FREE_CHAT_SYNCHRONIZED -> -1
            else -> 0
        }
    }
}