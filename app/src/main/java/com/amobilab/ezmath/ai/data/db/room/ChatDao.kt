package com.amobilab.ezmath.ai.data.db.room

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update

@Dao
interface ChatDao {
    @Query("SELECT * FROM chat_table WHERE historyId = :historyId")
    suspend fun getChatsForHistory(historyId: Long): List<ChatEntity>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertChat(chat: ChatEntity)

    @Update
    suspend fun updateChat(chat: ChatEntity)

    @Query("DELETE FROM chat_table WHERE historyId = :historyId")
    suspend fun deleteChatsForHistory(historyId: Long)

    // Hàm xóa nhiều item đã chọn
    @Query("DELETE FROM chat_table WHERE historyId IN (:historyIds)")
    suspend fun deleteChatsForHistories(historyIds: List<Long>)

    // L<PERSON>y tin nhắn cuối cùng của một lịch sử chat
    @Query("SELECT * FROM chat_table WHERE historyId = :historyId ORDER BY chatListId DESC LIMIT 1")
    suspend fun getLastChat(historyId: Long): ChatEntity?
}