package com.amobilab.ezmath.ai.presentation.ui.feature_screen.base

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.compose.foundation.AppButton
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppGlideImage
import amobi.module.compose.foundation.AppIcon
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppRowCentered
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.foundation.AppTextAutoSize
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontFamily
import amobi.module.compose.theme.AppFontSize
import amobi.module.compose.theme.AppSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.models.PromptSuggestion
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.shared_components.ModelAiModeBottomSheet
import com.amobilab.ezmath.ai.presentation.common.shared_components.SuggestionRow
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.NavigatorViewModel
import java.util.Locale
import kotlin.math.roundToLong

/**
 * Data class to represent a field in the feature screen
 */
data class FeatureField(
    val id: String,
    val label: String,
    val suggestions: List<PromptSuggestion>,
    var value: String = ""
)

/**
 * Base component for feature screens with similar structure
 */
@Composable
fun FeatureScreenBase(
    title: String,
    fields: List<FeatureField>,
    generateButtonText: String,
    generateOutline: (Map<String, String>) -> String,
    onSend: (prompt: String, imageUri: String) -> Unit,
    contentAboveFields: @Composable () -> Unit = {},
    requiredFields: List<String> = emptyList(),
) {
    // Common gradient brush
    val gradientBrush = Brush.linearGradient(
        colors = listOf(Color(0xFF81D8D0), Color(0xFF0ABAB5)),
        start = Offset(0f, 0f),
        end = Offset(0f, 100f)
    )

    // Context
    val context = LocalContext.current

    // Navigation
    val navigatorViewModel = NavigatorViewModel.getInstance()

    var isNavigatingBack by remember { mutableStateOf(false) }

    // Track focused text field
    var focusedTextField by remember { mutableStateOf<String?>(null) }

    // Track field that needs cursor at the end
    var fieldNeedsCursorAtEnd by remember { mutableStateOf<String?>(null) }

    // Kiểm soát hiển thị gợi ý
    var showSuggestion by remember { mutableStateOf(true) }

    // Create state for each field
    val fieldValues = remember {
        fields.associate { it.id to mutableStateOf(it.value) }
    }

    // Theo dõi các trường có lỗi (chưa nhập)
    val fieldErrors = remember {
        fields.associate { it.id to mutableStateOf(false) }
    }

    var selectedModelIcon by remember {
        mutableIntStateOf(
            when(PrefAssist.getString(PrefConst.MODEL_AI))
            {
                ModelAiMode.GEMINI.name -> R.drawable.ic_gemini
                ModelAiMode.GPT.name -> R.drawable.ic_chat_gpt
                else -> R.drawable.ic_chat_gpt
            }
        )
    }

    val showBottomSheetSelectModel = remember { mutableStateOf(false) }

    Scaffold { innerPadding ->
        AppColumn(
            modifier = Modifier.fillMaxSize()
        ) {
            // App bar
            AppRowCentered(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.background)
                    .height(AppSize.APPBAR_HEIGHT + innerPadding.calculateTopPadding())
                    .zIndex(200f)
                    .background(color = AppColors.current.actionBarColor)
                    .padding(top = innerPadding.calculateTopPadding()),
            ) {
                AppIcon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_back),
                    tint = AppColors.current.text,
                    clickZone = AppSize.MIN_TOUCH_SIZE,
                ) {
                    if (!isNavigatingBack) {
                        isNavigatingBack = true
                        navigatorViewModel.navigateBack()
                    }
                }
                AppText(
                    text = title,
                    modifier = Modifier
                        .weight(1f),
                    fontSize = AppFontSize.TITLE,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Start,
                    color = AppColors.current.text
                )

                AppRow(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    AppGlideImage(
                        resId = R.drawable.svg_ic_coin_2,
                        modifier = Modifier.size(20.dp)
                    )
                    AppSpacer(4.dp)
                    val coinTotal = PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE)
                    AppText(
                        text =
                            if (coinTotal > 1000000)
                                "${String.format(Locale.getDefault(), "%,d", (coinTotal.toDouble() / 1000000).roundToLong())}M"
                            else if (coinTotal > 10000)
                                "${String.format(Locale.getDefault(), "%,d", (coinTotal.toDouble() / 1000).roundToLong())}K"
                            else String.format(Locale.getDefault(), "%,d", coinTotal)
                        ,
                        fontSize = AppFontSize.BODY2,
                        fontWeight = FontWeight.W700,
                        lineHeight = 20.sp,
                        color = AppColors.current.text
                    )

                    AppSpacer(12.dp)

                    Image(
                        painter = painterResource(selectedModelIcon),
                        modifier = Modifier
                            .size(24.dp)
                            .clip(RoundedCornerShape(50))
                            .clickable {
                                showBottomSheetSelectModel.value = true
                            },
                        contentDescription = null,
                    )
                }
                AppSpacer(16.dp)
            }
            ModelAiModeBottomSheet(
                showIt = showBottomSheetSelectModel.value,
                onDismissRequest = { selected ->
                    selected?.let {
                        selectedModelIcon = it.iconRes
                    }
                    showBottomSheetSelectModel.value = false
                }
            )

            // Content above fields
            contentAboveFields()

            // Fields section
            AppColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                // Render each field
                fields.forEachIndexed { index, field ->
                    // Determine if field should be multiline based on index and field type
                    val isMultiline = index == 0 || // First field is always multiline
                            field.id.contains("topic", ignoreCase = true) || // Topic fields are multiline
                            field.id.contains("analysis", ignoreCase = true) || // Analysis fields are multiline
                            field.id.contains("goal", ignoreCase = true) // Goal fields are multiline

                    FeatureTextField(
                        id = field.id,
                        label = field.label,
                        value = fieldValues[field.id]?.value ?: "",
                        onValueChange = { newValue ->
                            fieldValues[field.id]?.value = newValue
                            // Xóa trạng thái lỗi khi người dùng nhập vào trường
                            if (newValue.isNotBlank() && fieldErrors[field.id]?.value == true) {
                                fieldErrors[field.id]?.value = false
                            }
                        },
                        onFocusChanged = { isFocused ->
                            focusedTextField = if (isFocused) field.id else null
                            // Reset fieldNeedsCursorAtEnd sau khi đã focus vào trường
                            if (isFocused) {
                                if (fieldNeedsCursorAtEnd == field.id) {
                                    fieldNeedsCursorAtEnd = null
                                }
                                // Hiển thị lại gợi ý khi focus vào trường
                                showSuggestion = true
                            }
                        },
                        isMultiline = isMultiline,
                        isRequired = requiredFields.contains(field.id), // Đánh dấu trường bắt buộc
                        hasError = fieldErrors[field.id]?.value ?: false, // Hiển thị viền đỏ nếu có lỗi
                        needsCursorAtEnd = fieldNeedsCursorAtEnd == field.id, // Đặt con trỏ ở cuối văn bản nếu cần
                        placeholder = when (field.id) {
                            "topic" -> stringResource(R.string.placeholder_topic)
                            "type" -> stringResource(R.string.placeholder_essay_type)
                            "wordCount" -> stringResource(R.string.placeholder_word_count)
                            "language" -> stringResource(R.string.placeholder_language_tone)
                            "title" -> stringResource(R.string.placeholder_title)
                            "author" -> stringResource(R.string.placeholder_author)
                            "analysis" -> stringResource(R.string.placeholder_analysis_type)
                            "format" -> stringResource(R.string.placeholder_format)
                            "academicLevel" -> stringResource(R.string.placeholder_academic_level)
                            "goal" -> stringResource(R.string.placeholder_research_goal)
                            "sources" -> stringResource(R.string.placeholder_preferred_sources)
                            "depth" -> stringResource(R.string.placeholder_depth_length)
                            else -> stringResource(R.string.placeholder_default)
                        }
                    )
                    AppSpacer(16.dp)
                }
            }

            // Suggestions and button section
            AppColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .imePadding()
            ) {
                // Show suggestions based on focused text field
                val focusedField = fields.find { it.id == focusedTextField }
                if (focusedField != null && showSuggestion) {
                    SuggestionRow(
                        suggestions = focusedField.suggestions,
                        onSuggestionClick = { suggestion ->
                            fieldValues[focusedField.id]?.value = suggestion
                            // Đánh dấu trường này cần đặt con trỏ ở cuối văn bản
                            fieldNeedsCursorAtEnd = focusedField.id
                            // Ẩn gợi ý sau khi đã chọn
                            showSuggestion = false
                        }
                    )
                }

                AppSpacer(16.dp)

                // Kiểm tra xem tất cả các trường bắt buộc đã được nhập chưa
                val allRequiredFieldsFilled = if (requiredFields.isEmpty()) {
                    true // Nếu không có trường bắt buộc, luôn cho phép nhấn nút
                } else {
                    requiredFields.all { fieldId ->
                        val fieldValue = fieldValues[fieldId]?.value ?: ""
                        fieldValue.isNotBlank()
                    }
                }

                // Thông báo lỗi nếu có
                var errorMessage by remember { mutableStateOf("") }

                // Generate button
                AppButton(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    onClick = {
                        if (allRequiredFieldsFilled) {
                            // Collect all field values
                            val fieldMap = fieldValues.mapValues { it.value.value }

                            // Generate outline
                            val outline = generateOutline(fieldMap)

                            // Send to chat
                            onSend(
                                outline,
                                ""
                            )
                        } else {
                            // Đánh dấu các trường chưa nhập là có lỗi
                            val missingFieldIds = requiredFields.filter { fieldId ->
                                val fieldValue = fieldValues[fieldId]?.value ?: ""
                                fieldValue.isBlank()
                            }

                            // Cập nhật trạng thái lỗi cho các trường
                            missingFieldIds.forEach { fieldId ->
                                fieldErrors[fieldId]?.value = true
                            }

                            // Hiển thị thông báo lỗi
                            val missingFieldLabels = missingFieldIds.mapNotNull { fieldId ->
                                fields.find { it.id == fieldId }?.label
                            }.joinToString(", ")

                            errorMessage = context.getString(R.string.required_fields_message, missingFieldLabels)

                            MixedUtils.showToast(errorMessage)
                        }
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Transparent,
                        contentColor = AppColors.current.onText
                    ),
                    shape = RoundedCornerShape(8.dp),
                    enabled = true, // Luôn cho phép nhấn nút để hiển thị viền đỏ cho các trường chưa nhập
                    contentPadding = PaddingValues(0.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                brush = gradientBrush,
                                shape = RoundedCornerShape(8.dp)
                            )
                            .padding(horizontal = 16.dp, vertical = 12.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        AppTextAutoSize(
                            text = generateButtonText,
                            fontSize = AppFontSize.BODY1,
                            fontWeight = FontWeight.W500,
                            color = AppColors.current.onText,
                            lineHeight = 24.sp
                        )
                    }
                }
                AppSpacer(16.dp)
            }
        }
    }
}

/**
 * Reusable text field component for feature screens
 */
@Composable
fun FeatureTextField(
    id: String,
    label: String,
    value: String,
    onValueChange: (String) -> Unit,
    onFocusChanged: (Boolean) -> Unit,
    placeholder: String = stringResource(R.string.placeholder_default),
    isMultiline: Boolean = false,
    isRequired: Boolean = false,
    hasError: Boolean = false,
    needsCursorAtEnd: Boolean = false
) {
    if (isRequired) {
        // Hiển thị nhãn với dấu sao màu đỏ cho trường bắt buộc

        Text(
            modifier = Modifier.padding(start = 12.dp),
            text = buildAnnotatedString {
                append(label.uppercase())
                withStyle(style = SpanStyle(color = Color.Red)) {
                    append(" *")
                }
            },
            color = AppColors.current.titleContent,
            fontSize = AppFontSize.BODY2,
            lineHeight = 20.sp,
            fontWeight = FontWeight.W400,
            fontFamily = AppFontFamily.get()
        )
    } else {
        // Hiển thị nhãn bình thường cho trường không bắt buộc
        AppText(
            modifier = Modifier.padding(start = 12.dp),
            text = label.uppercase(),
            color = AppColors.current.titleContent,
            fontSize = AppFontSize.BODY2,
            lineHeight = 20.sp,
            fontWeight = FontWeight.W400,
        )
    }
    AppSpacer(4.dp)
    // Sử dụng TextField với viền màu đỏ khi có lỗi
    // Theo dõi vị trí con trỏ hiện tại
    var currentTextFieldValue by remember { mutableStateOf(TextFieldValue(text = value)) }

    // Theo dõi xem trường này đã từng được focus chưa
    var hasBeenFocused by remember { mutableStateOf(false) }

    // Chỉ cập nhật vị trí con trỏ khi cần (khi chọn gợi ý)
    LaunchedEffect(needsCursorAtEnd) {
        if (needsCursorAtEnd) {
            // Đặt con trỏ ở cuối văn bản khi chọn gợi ý
            currentTextFieldValue = TextFieldValue(
                text = value,
                selection = TextRange(value.length)
            )
        }
    }

    // Cập nhật nội dung văn bản khi giá trị thay đổi từ bên ngoài
    LaunchedEffect(value) {
        if (currentTextFieldValue.text != value && !needsCursorAtEnd) {
            // Cập nhật text nhưng giữ nguyên vị trí con trỏ nếu có thể
            val newPosition = minOf(currentTextFieldValue.selection.start, value.length)
            currentTextFieldValue = TextFieldValue(
                text = value,
                selection = TextRange(newPosition)
            )
        }
    }

    BasicTextField(
        value = currentTextFieldValue,
        onValueChange = { newValue ->
            // Cập nhật giá trị mới và giữ nguyên vị trí con trỏ
            currentTextFieldValue = newValue
            onValueChange(newValue.text)
        },
        textStyle = TextStyle(
            color = AppColors.current.text,
            fontSize = AppFontSize.BODY2,
            fontWeight = FontWeight.W400,
            lineHeight = 20.sp
        ),
        minLines = if (isMultiline) 5 else 2,
        maxLines = if (isMultiline) 5 else 3,
        decorationBox = { innerTextField ->
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(8.dp))
                    .background(
                        AppColors.current.backgroundInputChatBotCompose,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .then(
                        if (hasError)
                            Modifier.border(
                                1.dp,
                                Color.Red,
                                shape = RoundedCornerShape(8.dp)
                            )
                        else
                            Modifier
                    )
                    .padding(horizontal = 12.dp, vertical = 8.dp)
            ) {
                if (value.isEmpty()) {
                    AppText(
                        text = placeholder,
                        color = if (hasError) Color.Red.copy(alpha = 0.6f) else AppColors.current.textHintColor,
                        fontSize = AppFontSize.BODY2,
                        fontWeight = FontWeight.W400,
                        textAlign = TextAlign.Start,
                        lineHeight = 20.sp,
                        maxLines = 2,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
                innerTextField()
            }
        },
        modifier = Modifier
            .fillMaxWidth()
            .onFocusChanged {
                val isFocused = it.isFocused
                onFocusChanged(isFocused)

                // Chỉ đặt con trỏ ở cuối văn bản khi focus lần đầu tiên
                if (isFocused && !hasBeenFocused) {
                    currentTextFieldValue = TextFieldValue(
                        text = currentTextFieldValue.text,
                        selection = TextRange(currentTextFieldValue.text.length)
                    )
                    hasBeenFocused = true
                }
            }
    )
}
