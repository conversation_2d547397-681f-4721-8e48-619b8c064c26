package com.amobilab.ezmath.ai.presentation.ui.onboarding

import amobi.module.common.advertisements.interstitial_ad.AdvertsManagerInter
import amobi.module.common.views.CommActivity
import amobi.module.compose.extentions.paddingHorizontal
import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.presentation.common.shared_components.VideoBackground
import kotlinx.coroutines.launch

@Composable
fun OnboardingCompose(
    onFinish: () -> Unit,
) {
    val context = LocalContext.current
    val viewModel = hiltViewModel<OnboardingViewModel>()
    val pagerState = rememberPagerState(pageCount = { 3 })
    val coroutineScope = rememberCoroutineScope()

    LaunchedEffect(pagerState.currentPage) {
        if (pagerState.currentPage == 2 &&
            AdvertsManagerInter.fullOpenAds?.isAdvertsAvailable == true
        ) {
            AdvertsManagerInter.showInterFullAds(
                activity = context as CommActivity,
                fullInterAds = AdvertsManagerInter.fullOpenAds,
            )
        }
    }

    val onboardingPages = listOf(
        OnboardingPage(
            video = R.raw.bg_onboarding_1,
            title = stringResource(id = R.string.onboarding_title_1),
            description = stringResource(id = R.string.onboarding_desc_1)
        ),
        OnboardingPage(
            video = R.raw.bg_onboarding_2,
            title = stringResource(id = R.string.onboarding_title_2),
            description = stringResource(id = R.string.onboarding_desc_2)
        ),
        OnboardingPage(
            video = R.raw.bg_onboarding_3,
            title = stringResource(id = R.string.onboarding_title_3),
            description = stringResource(id = R.string.onboarding_desc_3)
        )
    )

    Scaffold(
        modifier = Modifier.fillMaxSize()
    ) { innerPadding ->
        // background
//        Image(
//            painter = painterResource(id = R.drawable.bg_on_2),
//            contentDescription = null,
//            modifier = Modifier.fillMaxSize()
//        )

        Box(
            modifier = Modifier
                .fillMaxSize()
        ) {
            // Pager content
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.fillMaxSize()
            ) { page ->
                OnboardingPageContent(
                    page = onboardingPages[page],
                    innerPadding = innerPadding
                )
            }

            // Skip button
//            if (pagerState.currentPage < 2) {
//                Text(
//                    text = stringResource(id = R.string.skip),
//                    modifier = Modifier
//                        .align(Alignment.TopEnd)
//                        .padding(16.dp)
//                        .padding(top = innerPadding.calculateTopPadding())
//                        .clickable {
//                            viewModel.setOnboardingCompleted()
//                            onFinish()
//                        },
//                    color = AppColors.current.text,
//                    fontSize = 16.sp
//                )
//            }

            // Bottom section with indicators and buttons

            // Bố trí Page indicators bên trái và nút Next bên phải
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
                    .paddingHorizontal(16.dp)
                    .padding(bottom = innerPadding.calculateBottomPadding())
                    .padding(bottom = 36.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Page indicators bên trái
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    repeat(pagerState.pageCount) { iteration ->
                        val color = if (pagerState.currentPage == iteration) {
                            AppColors.current.buttonColor
                        } else {
                            Color.LightGray
                        }
                        val width = if (pagerState.currentPage == iteration) {
                            32.dp // Chỉ báo trang hiện tại dài hơn
                        } else {
                            8.dp // Chỉ báo trang khác ngắn hơn
                        }
                        Box(
                            modifier = Modifier
                                .padding(horizontal = 4.dp)
                                .clip(CircleShape)
                                .background(color)
                                .height(8.dp)
                                .width(width)
                        )
                    }
                }

                // Nút Next với viền tiến trình bên phải
                CircularProgressButton(
                    progress = (pagerState.currentPage + 1).toFloat() / pagerState.pageCount,
                    onClick = {
                        if (pagerState.currentPage == 2) {
                            viewModel.setOnboardingCompleted()
                            onFinish()
                        } else {
                            coroutineScope.launch {
                                pagerState.animateScrollToPage(pagerState.currentPage + 1)
                            }
                        }
                    }
                )

            }
        }
    }

    BackHandler { }

}

@Composable
fun OnboardingPageContent(
    page: OnboardingPage,
    innerPadding: PaddingValues
) {
    AppBox(
        modifier = Modifier
            .fillMaxSize()
    ) {
        // Background image
//        Image(
//            painter = painterResource(id = R.drawable.bg_on_2),
//            contentDescription = null,
//            modifier = Modifier.fillMaxSize()
//        )

        // mp4
        VideoBackground(
            videoResId = page.video,
            modifier = Modifier.fillMaxSize(),
        )

        //json
//        LottieBackground(
//            animationResId = R.raw.optimized_map8ae0p,
//            modifier = Modifier.fillMaxSize(),
//            backgroundImageResId = R.drawable.bg_on_2
//        )

        // gif
//        GifBackground(
//            gifResId = page.video,
//            modifier = Modifier.fillMaxSize(),
//            backgroundImageResId = R.drawable.bg_on_2
//        )

        AppColumn(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .padding(bottom = innerPadding.calculateBottomPadding())
                .padding(horizontal = 24.dp),
        ) {
            // Title
            AppText(
                text = page.title,
                fontSize = AppFontSize.BIG,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Start,
                lineHeight = 40.sp,
                color = Color.White,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // Description
            AppText(
                text = page.description,
                fontSize = AppFontSize.HEADER,
                textAlign = TextAlign.Start,
                lineHeight = 24.sp,
                color = Color.White,
            )

            AppSpacer(height = 160.dp)
        }
    }
}

data class OnboardingPage(
    val video: Int,
    val title: String,
    val description: String
)

@Composable
fun CircularProgressButton(
    progress: Float,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    size: Dp = 75.dp,
    buttonSize: Dp = 56.dp,
    progressColor: Color = AppColors.current.buttonColor
) {
    val gradientBrush = Brush.linearGradient(
        colors = listOf(Color(0xFF81D8D0), Color(0xFF0ABAB5)),
        start = Offset(0f, 0f),
        end = Offset(0f, 100f)
    )

    Box(
        modifier = modifier
            .size(size),
        contentAlignment = Alignment.Center
    ) {
        // Viền tiến trình - nằm bên ngoài và lớn hơn nút
        CircularProgressIndicator(
            progress = { progress },
            modifier = Modifier.size(size),
            color = progressColor,
            trackColor = Color.Transparent,
            strokeWidth = 2.dp
        )

        // Nút hình tròn ở giữa - nhỏ hơn viền tiến trình
        Box(
            modifier = Modifier
                .size(buttonSize)
                .clip(CircleShape)
                .background(
                    brush = gradientBrush,
                    shape = RoundedCornerShape(8.dp)
                )
                .clickable(onClick = onClick),
            contentAlignment = Alignment.Center
        ) {
            // Biểu tượng mũi tên
            Icon(
                painter = painterResource(id = R.drawable.svg_comm_ic_arrow_right),
                contentDescription = stringResource(id = R.string.next),
                modifier = Modifier
                    .size(24.dp),
                tint = Color.White
            )
        }
    }
}
