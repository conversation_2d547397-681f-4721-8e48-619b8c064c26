package com.amobilab.ezmath.ai.app

import androidx.lifecycle.ViewModel
import com.amobilab.ezmath.ai.data.network.services.GatewayApi
import com.amobilab.ezmath.ai.data.network.services.GeminiApi
import com.amobilab.ezmath.ai.data.network.services.GptApi
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
open class BaseViewModel @Inject constructor() : ViewModel() {
    @Inject
    lateinit var geminiApi: GeminiApi

    @Inject
    lateinit var gptApi: GptApi

    @Inject
    lateinit var gatewayApi: GatewayApi

    @Inject
    lateinit var coinViewModel: CoinViewModel
}