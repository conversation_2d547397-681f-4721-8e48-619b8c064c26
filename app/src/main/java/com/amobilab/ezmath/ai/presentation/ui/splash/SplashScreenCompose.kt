package com.amobilab.ezmath.ai.presentation.ui.splash

import amobi.module.compose.foundation.AppColumnCentered
import amobi.module.compose.foundation.AppSpacer
import android.annotation.SuppressLint
import android.text.Html
import android.text.method.LinkMovementMethod
import android.widget.TextView
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.res.ResourcesCompat
import androidx.core.text.HtmlCompat
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.presentation.common.shared_components.VideoBackground
import com.amobilab.ezmath.ai.values.Const

@SuppressLint("UnusedMaterial3ScaffoldPaddingParameter")
@Composable
fun SplashScreenCompose() {

    Scaffold { innerPadding ->
        // Video background với hình ảnh background khi video chưa load xong
        VideoBackground(
            videoResId = R.raw.background_splash,
            modifier = Modifier.fillMaxSize(),
        )

        // Nội dung chính
        AppColumnCentered(
            modifier = Modifier.fillMaxSize()
        ) {

            AppSpacer(Modifier.weight(1f))

            AndroidView(
                modifier = Modifier
                    .padding(horizontal = 16.dp),
                factory = { context ->
                    val hyperLink = "<a href='${Const.POLICY_URL}'>${
                        context.getString(R.string.terms_and_services)
                    }</a>"
                    var html = "<p>" + context.getString(
                        R.string.accept_terms_here,
                        hyperLink
                    ) + "</p>"
                    // if hyper not working, add it to the end of the string
                    if (!html.contains(hyperLink)) {
                        html += "<p><a href='${Const.POLICY_URL}'>${
                            context.getString(
                                R.string.terms_and_services
                            )
                        }</a></p>"
                    }
                    TextView(context).apply {
                        text = Html.fromHtml(html, HtmlCompat.FROM_HTML_MODE_LEGACY)
                        typeface = ResourcesCompat.getFont(
                            context, amobi.module.compose.theme.R.font.roboto_medium
                        )
                        setTextColor(Color.White.toArgb()) // Đổi màu chữ thành trắng

                        movementMethod = LinkMovementMethod.getInstance()
                    }
                }
            )
        }
    }

    BackHandler { }
}