package com.amobilab.ezmath.ai.presentation.ui.user_info

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.compose.complex_views.AppConfirmDialog
import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.foundation.AppBoxCentered
import amobi.module.compose.foundation.AppButton
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import amobi.module.compose.theme.AppSize
import amobi.module.compose.theme.AppThemeWrapper
import android.app.Activity.RESULT_OK
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.share_dialog.CommonDialog
import com.amobilab.ezmath.ai.presentation.common.shared_components.AppAppbar
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.MainDataViewModel
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.NavigatorViewModel
import com.amobilab.ezmath.ai.utils.GoogleAuthUiClient
import com.google.android.gms.auth.api.identity.Identity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@AppPreview
@Composable
fun UserInfoScreenPreview() {
    PreviewAssist.initVariables(LocalContext.current)
    AppThemeWrapper {
        UserDeleteScreen(
            userId = "123",
            userName = "Nguyễn Văn A",
            profilePictureUrl = "",
            coin = 100,
            phoneNumber = "0123456789",
            email = "<EMAIL>"
        )
    }
}

@Composable
fun UserDeleteScreen(
    userId: String?,
    userName: String?,
    profilePictureUrl: String?,
    phoneNumber: String?,
    email: String?,
    coin: Long,
) {
    val navigatorViewModel = NavigatorViewModel.getInstance()

    val mainDataViewModel = hiltViewModel<MainDataViewModel>()

    var showDialog by remember { mutableStateOf(false) }
    var showDialogSignAlign by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }
    val context = LocalContext.current

    val coroutineScope = rememberCoroutineScope()

    val googleAuthUiClient by lazy {
        GoogleAuthUiClient(
            oneTapClient = Identity.getSignInClient(context),
            viewModel = mainDataViewModel
        )
    }

    var signedInUser by remember { mutableStateOf(googleAuthUiClient.getSignedInUser()) }

    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult(),
        onResult = { result ->
            if (result.resultCode == RESULT_OK) {
                isLoading = true
                coroutineScope.launch {
                    val signInResult = googleAuthUiClient.signInWithIntent(
                        intent = result.data ?: return@launch
                    )
                    signedInUser = googleAuthUiClient.getSignedInUser()
//                    viewModel.onSignInResult(signInResult)
                    isLoading = false
                    navigatorViewModel.navigateBack()
                }
            }
        }
    )

    var isNavigatingBack by remember { mutableStateOf(false) }

    Scaffold { innerPadding ->
        AppColumn(Modifier.fillMaxSize()) {
            AppAppbar(
                innerPadding = innerPadding,
                title = stringResource(R.string.delete_account),
                onBack = {
                    if (!isNavigatingBack) {
                        isNavigatingBack = true
                        navigatorViewModel.navigateBack()
                    }
                }
            )
            AppColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
                    .padding(top = 24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceBetween
            ) {
                AppColumn(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    AppSpacer(8.dp)
                    Image(
                        modifier = Modifier.fillMaxWidth(0.6f),
                        painter = painterResource(R.drawable.ic_bg_delete_account),
                        contentDescription = null,
                    )

                    AppSpacer(24.dp)

                    AppText(
                        text = stringResource(R.string.sure_you_want_to_delete_account),
                        fontSize = AppFontSize.BODY1,
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center,
                        lineHeight = 24.sp,
                        color = AppColors.current.text
                    )

                    AppSpacer(16.dp)
                    AppText(
                        text = stringResource(R.string.deleting_your_account_will_erase_all_chat_history_personal_settings_and_related_data_this_action_cannot_be_undone),
                        fontSize = AppFontSize.BODY1,
                        fontWeight = FontWeight.W400,
                        lineHeight = 24.sp,
                        textAlign = TextAlign.Center,
                        color = AppColors.current.text
                    )
                }

                AppSpacer(modifier = Modifier.weight(1f))
                AppButton(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(AppSize.MIN_TOUCH_SIZE)
                        .background(
                            color = AppColors.current.textError,
                            shape = RoundedCornerShape(8.dp)
                        ),
                    onClick = {
                        showDialog = true
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Transparent,
                        contentColor = AppColors.current.onText
                    ),
                    shape = RoundedCornerShape(8.dp),
                    enabled = true,
                    contentPadding = PaddingValues(0.dp)
                ) {
                    AppText(
                        text = stringResource(R.string.delete_account),
                        fontSize = AppFontSize.BODY1,
                        fontWeight = FontWeight.W500,
                        color = Color.White,
                        lineHeight = 24.sp
                    )
                }

                AppSpacer(24.dp)
            }

            if (showDialog) {
                CommonDialog(
                    title = stringResource(R.string.confirm_account_deletion_title),
                    message = stringResource(R.string.confirm_account_deletion_warning) + "\n" + stringResource(R.string.confirm_account_deletion_notes),
                    confirmText = stringResource(R.string.txtid_delete),
                    dismissText = stringResource(R.string.txtid_cancel),
                    onConfirm = {
                        showDialog = false
                        isLoading = true
                        CoroutineScope(Dispatchers.Main).launch {
                            if (googleAuthUiClient.deleteAccount()) {
                                navigatorViewModel.navigateBack()
                                MixedUtils.showToast(context, R.string.the_account_has_been_deleted)
                                mainDataViewModel.coinViewModel.setCoinBalance(0)
                                PrefAssist.setString(PrefConst.USER_ID, "")
                            } else {
                                showDialogSignAlign = true
                                MixedUtils.showToast(context, R.string.account_deletion_failed)
                            }
                            isLoading = false
                        }
                    },
                    onDismiss = {
                        showDialog = false
                    }
                )
            }
        }

        if (showDialogSignAlign) {
            AppConfirmDialog(
                title = stringResource(R.string.log_in_again),
                message = stringResource(R.string.log_in_to_delete_account),
                positiveButtonText = stringResource(R.string.sign_in),
                negativeButtonText = stringResource(R.string.txtid_cancel),
                onPositiveClick = {
                    showDialogSignAlign = false
                    coroutineScope.launch {
                        val signInIntentSender = googleAuthUiClient.signIn()
                        launcher.launch(
                            IntentSenderRequest.Builder(signInIntentSender ?: return@launch).build()
                        )
                    }
                },
                onNegativeClick = { showDialogSignAlign = false },
                onDismissRequest = { showDialogSignAlign = false }
            )

        }
        if (isLoading) {
            AppBoxCentered(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f))
                    .clickable(enabled = false) {},
            ) {
                CircularProgressIndicator()
            }
        }
    }
}
