package com.amobilab.ezmath.ai.utils

import com.google.firebase.FirebaseApp
import com.google.firebase.appcheck.AppCheckProvider
import com.google.firebase.appcheck.AppCheckProviderFactory
import com.google.firebase.appcheck.debug.InternalDebugSecretProvider
import com.google.firebase.appcheck.debug.internal.DebugAppCheckProvider
import com.google.firebase.inject.Provider
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadFactory
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import com.amobilab.ezmath.ai.BuildConfig

class CustomAppCheckProviderFactory : AppCheckProviderFactory {
    companion object {
        private val instance = CustomAppCheckProviderFactory()
        @JvmStatic
        fun getInstance(): CustomAppCheckProviderFactory {
            return instance
        }
    }

    internal class CustomProvider : Provider<InternalDebugSecretProvider> {
        private val internalDebugSecretProvider = CustomDebugSecretProvider()
        override fun get(): InternalDebugSecretProvider {
            return internalDebugSecretProvider
        }
    }

    internal class CustomDebugSecretProvider : InternalDebugSecretProvider {
        override fun getDebugSecret(): String {
            return BuildConfig.DEBUG_APP_CHECK
        }
    }

    internal class SimpleThreadFactory : ThreadFactory {
        override fun newThread(r: Runnable?): Thread {
            return Thread(r)
        }
    }

    private val NUMBER_OF_CORES = 1
    private val backgroundPriorityThreadFactory = SimpleThreadFactory()

    private val executor = ThreadPoolExecutor(
        NUMBER_OF_CORES * 2,
        NUMBER_OF_CORES * 2,
        60L,
        TimeUnit.SECONDS,
        LinkedBlockingQueue(),
        backgroundPriorityThreadFactory
    )
    private val provider = CustomProvider()
    override fun create(firebaseApp: FirebaseApp): AppCheckProvider {
        return DebugAppCheckProvider(
            firebaseApp,
            provider,
            executor,
            executor,
            executor
        )
    }
}
