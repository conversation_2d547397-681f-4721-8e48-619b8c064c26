package com.amobilab.ezmath.ai.presentation.common.shared_components

import amobi.module.compose.utils.toDp
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp

@Composable
fun ScanningLine(modifier: Modifier = Modifier) {
    var parentSize by remember { mutableStateOf(IntSize.Zero) }

    val infiniteTransition = rememberInfiniteTransition()
    val animationProgress by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 1500, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        )
    )

    // Chiều cao dải màu bằng 90% chiều cao view cha
    val scanningHeight = parentSize.height * 0.8f

    val brush = if (scanningHeight > 0) {
        Brush.linearGradient(
            0f to Color.Transparent,
            0.45f to Color(0xFFE6C083).copy(alpha = 1f),
            0.55f to Color(0xFFE6C083).copy(alpha = 1f),
            1f to Color.Transparent,
            start = Offset(0f, animationProgress * scanningHeight),
            end = Offset(0f, animationProgress * scanningHeight + scanningHeight * 0.02f)
        )
    } else {
        Brush.linearGradient(
            0f to Color.Transparent,
            1f to Color.Transparent
        )
    }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height((scanningHeight).toDp())  // Chuyển pixel sang dp
            .onSizeChanged { parentSize = it }
            .background(brush = brush)
    )
}
@Preview(showBackground = true)
@Composable
fun ScanningLine90PercentHeightPreview() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(300.dp)
            .padding(16.dp)
            .background(Color.Gray)
    ) {
        ScanningLine()
    }
}
