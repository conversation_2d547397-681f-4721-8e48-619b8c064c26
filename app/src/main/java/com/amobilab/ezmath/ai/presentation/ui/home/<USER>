package com.amobilab.ezmath.ai.presentation.ui.home

import amobi.module.common.advertisements.interstitial_ad.AdvertsManagerInter
import amobi.module.common.configs.CommFigs
import amobi.module.common.utils.dlog
import amobi.module.common.views.CommActivity
import amobi.module.compose.foundation.AppTextAutoSize
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import android.annotation.SuppressLint
import android.app.Activity
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.size
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.presentation.common.share_dialog.CommonDialog
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.NavigatorViewModel
import com.amobilab.ezmath.ai.presentation.navigation.ScreenRoutes
import com.amobilab.ezmath.ai.presentation.ui.home.tabs.ChatTabCompose
import com.amobilab.ezmath.ai.presentation.ui.home.tabs.HistoryChatTabCompose
import com.amobilab.ezmath.ai.presentation.ui.home.tabs.ScanTabCompose
import com.amobilab.ezmath.ai.presentation.ui.home.tabs.SettingsTabCompose
import com.amobilab.ezmath.ai.presentation.ui.home.values.BottomBarScreenRoutes

@Composable
@SuppressLint("UnusedMaterial3ScaffoldPaddingParameter", "CoroutineCreationDuringComposition")
fun HomeCompose() {
    val context = LocalContext.current

    val navigatorViewModel = NavigatorViewModel.getInstance()

    val navTabController = rememberNavController()

    val backStackEntryState by navTabController.currentBackStackEntryAsState()
    val currentRouteName = backStackEntryState
        ?.destination
        ?.route
        ?.substringAfterLast(".")
        ?.substringBefore("/")
        ?.split("?")
        ?.first()


    val borderColor = AppColors.current.divider2
    var isBottomBarVisible by remember { mutableStateOf(true) }

    val renderBottomBar = @Composable {
        if (isBottomBarVisible) {
            NavigationBar(
                modifier = Modifier
                    .drawBehind {
                        drawLine(
                            color = borderColor,
                            start = Offset(0f, 0f),
                            end = Offset(size.width, 0f),
                            strokeWidth = 2.dp.toPx()
                        )
                    },
                containerColor = AppColors.current.homeBottomNavBar,
                windowInsets = WindowInsets(0, 0, 0, 0),
            ) {
                val tabScreens = listOf(
                    BottomBarScreenRoutes.AiChat,
                    BottomBarScreenRoutes.Scan,
                    BottomBarScreenRoutes.History,
                    BottomBarScreenRoutes.Settings
                )
                tabScreens.forEach { currentTab ->
                    NavigationBarItem(
                        selected = currentRouteName == currentTab.screenRoute.route,
                        onClick = {
                            if (currentRouteName != currentTab.screenRoute.route) {
                                navTabController.navigate(currentTab.screenRoute.route) {
                                    popUpTo(0) {
                                        inclusive = true
                                    }
                                    launchSingleTop = true
                                }
                            }
                        },
                        icon = {
                            Image(
                                painter = painterResource(
                                    id = if (currentRouteName == currentTab.screenRoute.route)
                                        currentTab.iconActive
                                    else
                                        currentTab.iconInactive,
                                ),
                                modifier = Modifier
                                    .size(24.dp),
                                contentDescription = null,
                            )
                        },
                        label = {
                            AppTextAutoSize(
                                text = stringResource(currentTab.titleId),
                                color = if (currentRouteName == currentTab.screenRoute.route)
                                    AppColors.current.homeBottomNavBarActiveTint
                                else
                                    AppColors.current.homeBottomNavBarInactiveTint,
                                fontSize = AppFontSize.BODY2,
                                maxLines = 1,
                                fontWeight = if (currentRouteName == currentTab.screenRoute.route)
                                    FontWeight.W500
                                else
                                    FontWeight.W400,
                                textAlign = TextAlign.Center,
                            )
                        },
                        colors = NavigationBarItemDefaults.colors(
                            indicatorColor = Color.Transparent
                        )
                    )
                }
            }
        }
    }

    fun getPageIndexFromRoute(route: String): Int {
        dlog(route)
        dlog("getPageIndexFromRoute: ${ScreenRoutes.AiChatTab().route}")
        dlog("getPageIndexFromRoute: ${ScreenRoutes.ScanTab().route}")
        dlog("getPageIndexFromRoute: ${ScreenRoutes.HistoryTab().route}")
        dlog("getPageIndexFromRoute: ${ScreenRoutes.SettingTab().route}")
        return when (route) {
            ScreenRoutes.AiChatTab().route -> 0
            ScreenRoutes.ScanTab().route -> 1
            ScreenRoutes.HistoryTab().route -> 2
            ScreenRoutes.SettingTab().route -> 3
            else -> 1
        }
    }

    @Composable
    fun renderNavHost(innerPadding: PaddingValues) {
        val startRouteDestination =
            if (CommFigs.IS_SHOW_TEST_OPTION) // tssst
//            ScreenRoutes.AiChatTab()
//            ScreenRoutes.SettingTab()
//            ScreenRoutes.HistoryTab()
                ScreenRoutes.AiChatTab()
            else
                ScreenRoutes.AiChatTab()

        val initialRoute = startRouteDestination.route

        DisposableEffect(navTabController) {
            var destChangeCount = 0
            var lastAdsReloadMillis = 0L
            val listener = NavController.OnDestinationChangedListener { controller, destination, arguments ->
                destChangeCount++
                if (destChangeCount > 1) {
                    if (AdvertsManagerInter.fullActionAds?.isAdvertsAvailable == true) {
                        AdvertsManagerInter.showInterFullAds(
                            activity = context as CommActivity,
                            fullInterAds = AdvertsManagerInter.fullActionAds,
                        )
                    } else if (System.currentTimeMillis() - lastAdsReloadMillis > 10 * CommFigs.MILLIS_SECOND) {
                        lastAdsReloadMillis = System.currentTimeMillis()
                        AdvertsManagerInter.fullActionAds?.requestFullAds()
                    }
                }
            }
            navTabController.addOnDestinationChangedListener(listener)
            onDispose {
                navTabController.removeOnDestinationChangedListener(listener)
            }
        }

        NavHost(
            navController = navTabController,
            startDestination = initialRoute,
            enterTransition = {
                val currentTabIndex =
                    getPageIndexFromRoute(initialState.destination.route ?: initialRoute)
                val targetTabIndex =
                    getPageIndexFromRoute(targetState.destination.route ?: initialRoute)

                val direction = if (targetTabIndex > currentTabIndex)
                    AnimatedContentTransitionScope.SlideDirection.Left
                else
                    AnimatedContentTransitionScope.SlideDirection.Right

                slideIntoContainer(
                    towards = direction,
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    )
                ) + fadeIn(
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    )
                )
            },
            exitTransition = {
                val currentTabIndex =
                    getPageIndexFromRoute(initialState.destination.route ?: initialRoute)
                val targetTabIndex =
                    getPageIndexFromRoute(targetState.destination.route ?: initialRoute)

                val direction = if (targetTabIndex > currentTabIndex)
                    AnimatedContentTransitionScope.SlideDirection.Left
                else
                    AnimatedContentTransitionScope.SlideDirection.Right

                slideOutOfContainer(
                    towards = direction,
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    )
                ) + fadeOut(
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    )
                ) + scaleOut(
                    targetScale = 0.95f,
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    )
                )
            },
            popEnterTransition = {
                val currentTabIndex =
                    getPageIndexFromRoute(initialState.destination.route ?: initialRoute)
                val targetTabIndex =
                    getPageIndexFromRoute(targetState.destination.route ?: initialRoute)

                val direction = if (targetTabIndex > currentTabIndex)
                    AnimatedContentTransitionScope.SlideDirection.Left
                else
                    AnimatedContentTransitionScope.SlideDirection.Right

                slideIntoContainer(
                    towards = direction,
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    )
                ) + fadeIn(
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    )
                ) + scaleIn(
                    initialScale = 0.95f,
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    )
                )
            },
            popExitTransition = {
                val currentTabIndex =
                    getPageIndexFromRoute(initialState.destination.route ?: initialRoute)
                val targetTabIndex =
                    getPageIndexFromRoute(targetState.destination.route ?: initialRoute)

                val direction = if (targetTabIndex > currentTabIndex)
                    AnimatedContentTransitionScope.SlideDirection.Left
                else
                    AnimatedContentTransitionScope.SlideDirection.Right

                slideOutOfContainer(
                    towards = direction,
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    )
                ) + fadeOut(
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    )
                )
            }
        ) {
            composable(route = ScreenRoutes.ScanTab().route) {
                ScanTabCompose(
                    innerPadding,
                ) { scanMode, urlBitmap, isFromCamera, rectF ->
                    navigatorViewModel.navigateTo(
                        ScreenRoutes.ScanDetail(
                            scanMode = scanMode,
                            url = urlBitmap,
                            isFromCamera = isFromCamera,
                            rectFLeft = rectF.left,
                            rectFTop = rectF.top,
                            rectFRight = rectF.right,
                            rectFBottom = rectF.bottom
                        )
                    )
                }
            }

            composable(route = ScreenRoutes.AiChatTab().route) {
                ChatTabCompose(
                    innerPaddingHome = innerPadding,
                    nextMode = {
                        navTabController.popBackStack(
                            initialRoute,
                            false
                        )
                        navigatorViewModel.navigateTo(
                            ScreenRoutes.ChatScreen(
                                mode = it
                            )
                        )
                    },
                    onSend = { _mode, prompt, imgUrl ->
                        navigatorViewModel.navigateTo(
                            ScreenRoutes.ChatScreen(
                                mode = _mode,
                                ask = prompt,
                                urlBitmap = imgUrl
                            )
                        )
                    }
                )
            }

            composable(route = ScreenRoutes.HistoryTab().route) {
                HistoryChatTabCompose(
                    innerPaddingHome = innerPadding,
                    onClick = {
                        navTabController.popBackStack(
                            initialRoute,
                            false
                        )
                        navigatorViewModel.navigateTo(
                            ScreenRoutes.ChatScreen(
                                idHistory = it,
                                urlBitmap = "",
                                ask = "",
                                mode = ChatQuestionMode.Default
                            )
                        )
                    },
                    onOffBottomBar = {
                        isBottomBarVisible = it
                    }
                )
            }

            composable(route = ScreenRoutes.SettingTab().route) {
                SettingsTabCompose(innerPadding)
            }
        }
    }

    Scaffold(
        bottomBar = { renderBottomBar() }
    ) { innerPadding ->
        renderNavHost(innerPadding)
    }

    var showExitDialog by remember { mutableStateOf(false) }
    BackHandler(true) {
        isBottomBarVisible = true
        if (currentRouteName != ScreenRoutes.AiChatTab().route) {
            navTabController.navigate(ScreenRoutes.AiChatTab().route) {
                popUpTo(0) {
                    inclusive = true
                }
                launchSingleTop = true
            }
        } else {
            showExitDialog = true
        }
    }

    // Hiển thị dialog xác nhận thoát
    if (showExitDialog) {
        CommonDialog(
            title = stringResource(R.string.exit_app),
            message = stringResource(R.string.do_you_want_to_leave_now_don_t_forget_to_come_back_i_ll_be_waiting_for_you),
            confirmText = stringResource(R.string.txtid_ok),
            dismissText = stringResource(R.string.txtid_no),
            onConfirm = { (context as? Activity)?.finish() },
            onDismiss = {
                showExitDialog = false
            }
        )
    }
}
