package com.amobilab.ezmath.ai.data.db.powerSync

import com.powersync.PowerSyncDatabase
import com.powersync.PowerSyncException
import com.powersync.db.Queries
import com.powersync.db.SqlCursor
import com.powersync.db.getLongOptional
import com.powersync.db.getStringOptional
import com.powersync.db.internal.PowerSyncTransaction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import java.util.UUID

data class UserEntity(
    val id: String = "",
    val coin: Int
)

class UserRepository(private val database: PowerSyncDatabase) {

    // Mapper sử dụng các phương thức tiện ích của SqlCursor
    private val userEntityMapper: (SqlCursor) -> UserEntity = { cursor ->
        val columnMap = cursor.columnNames
        UserEntity(
            id = cursor.getString(columnMap["id"] ?: 0) ?: "",
            coin = cursor.getLong(columnMap["coin"] ?: 0)?.toInt() ?: 0
        )
    }

    // Chèn UserEntity, trả về userId
    suspend fun insertUser(user: UserEntity): String = withContext(Dispatchers.IO) {
        database.writeTransaction { transaction ->
            try {
                val id = UUID.randomUUID().toString()
                transaction.execute(
                    sql = """
                    INSERT OR REPLACE INTO user 
                    (id, coin) 
                    VALUES (?, ?)
                """.trimIndent(),
                    parameters = listOf(
                        id,
                        user.coin
                    )
                )
                id
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi chèn UserEntity: ${e.message}", e)
            }
        }
    }

    // Lấy UserEntity theo userId
    suspend fun getUserById(userId: String): UserEntity? = withContext(Dispatchers.IO) {
        try {
            database.getOptional(
                sql = "SELECT * FROM user WHERE id = ? LIMIT 1",
                parameters = listOf(userId),
                mapper = userEntityMapper
            )
        } catch (e: PowerSyncException) {
            throw RuntimeException("Lỗi khi lấy UserEntity: ${e.message}", e)
        } catch (e: IllegalArgumentException) {
            throw RuntimeException("Cột không hợp lệ: ${e.message}", e)
        }
    }

    // Lấy tất cả UserEntity
    suspend fun getAllUsers(): List<UserEntity> = withContext(Dispatchers.IO) {
        try {
            database.getAll(
                sql = "SELECT * FROM user",
                mapper = userEntityMapper
            )
        } catch (e: PowerSyncException) {
            throw RuntimeException("Lỗi khi lấy danh sách UserEntity: ${e.message}", e)
        } catch (e: IllegalArgumentException) {
            throw RuntimeException("Cột không hợp lệ: ${e.message}", e)
        }
    }

    // Cập nhật UserEntity
    suspend fun updateUser(user: UserEntity) = withContext(Dispatchers.IO) {
        database.writeTransaction { transaction ->
            try {
                transaction.execute(
                    sql = """
                        UPDATE user 
                        SET coin = ?
                        WHERE id = ?
                    """.trimIndent(),
                    parameters = listOf(
                        user.coin,
                        user.id
                    )
                )
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi cập nhật UserEntity: ${e.message}", e)
            }
        }
    }

    // Cập nhật số coin của user
    suspend fun updateUserCoin(userId: String, newCoinAmount: Int) = withContext(Dispatchers.IO) {
        database.writeTransaction { transaction ->
            try {
                transaction.execute(
                    sql = "UPDATE user SET coin = ? WHERE id = ?",
                    parameters = listOf(newCoinAmount, userId)
                )
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi cập nhật coin: ${e.message}", e)
            }
        }
    }

    // Tăng coin cho user
    suspend fun addCoinToUser(userId: String, coinAmount: Int) = withContext(Dispatchers.IO) {
        database.writeTransaction { transaction ->
            try {
                transaction.execute(
                    sql = "UPDATE user SET coin = coin + ? WHERE id = ?",
                    parameters = listOf(coinAmount, userId)
                )
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi thêm coin: ${e.message}", e)
            }
        }
    }

    // Trừ coin từ user
    suspend fun subtractCoinFromUser(userId: String, coinAmount: Int) = withContext(Dispatchers.IO) {
        database.writeTransaction { transaction ->
            try {
                transaction.execute(
                    sql = "UPDATE user SET coin = coin - ? WHERE id = ?",
                    parameters = listOf(coinAmount, userId)
                )
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi trừ coin: ${e.message}", e)
            }
        }
    }

    // Xóa UserEntity theo userId
    suspend fun deleteUser(userId: String) = withContext(Dispatchers.IO) {
        database.writeTransaction { transaction ->
            try {
                transaction.execute(
                    sql = "DELETE FROM user WHERE id = ?",
                    parameters = listOf(userId)
                )
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi xóa UserEntity: ${e.message}", e)
            }
        }
    }

    // Xóa tất cả UserEntity
    suspend fun deleteAllUsers() = withContext(Dispatchers.IO) {
        database.writeTransaction { transaction ->
            try {
                transaction.execute(sql = "DELETE FROM user")
            } catch (e: PowerSyncException) {
                throw RuntimeException("Lỗi khi xóa tất cả UserEntity: ${e.message}", e)
            }
        }
    }

    // Theo dõi thay đổi trong user table theo thời gian thực
    fun watchAllUsers(): Flow<List<UserEntity>> {
        return database.watch(
            sql = "SELECT * FROM user",
            mapper = userEntityMapper
        )
    }

    // Theo dõi một user cụ thể theo userId
    fun watchUserById(userId: String): Flow<UserEntity?> {
        return database.watchOptional(
            sql = "SELECT * FROM user WHERE id = ? LIMIT 1",
            parameters = listOf(userId),
            mapper = userEntityMapper
        )
    }

    // Theo dõi các bảng thay đổi (user table)
    fun onTablesChanged(): Flow<Set<String>> {
        return database.onChange(
            tables = setOf("user"),
            throttleMs = Queries.DEFAULT_THROTTLE.inWholeMilliseconds,
            triggerImmediately = true
        )
    }

    // Lấy user đầu tiên (nếu chỉ có một user trong hệ thống)
    suspend fun getFirstUser(): UserEntity? = withContext(Dispatchers.IO) {
        try {
            database.getOptional(
                sql = "SELECT * FROM user LIMIT 1",
                mapper = userEntityMapper
            )
        } catch (e: PowerSyncException) {
            throw RuntimeException("Lỗi khi lấy user đầu tiên: ${e.message}", e)
        } catch (e: IllegalArgumentException) {
            throw RuntimeException("Cột không hợp lệ: ${e.message}", e)
        }
    }

    // Theo dõi user đầu tiên
    fun watchFirstUser(): Flow<UserEntity?> {
        return database.watchOptional(
            sql = "SELECT * FROM user LIMIT 1",
            mapper = userEntityMapper
        )
    }

    // Kiểm tra xem có user nào tồn tại không
    suspend fun hasAnyUser(): Boolean = withContext(Dispatchers.IO) {
        try {
            val count = database.get(
                sql = "SELECT COUNT(*) as count FROM user"
            ) { cursor ->
                cursor.getLong(0) ?: 0L
            }
            count > 0
        } catch (e: PowerSyncException) {
            throw RuntimeException("Lỗi khi kiểm tra user: ${e.message}", e)
        }
    }
}
