package com.amobilab.ezmath.ai.presentation.ui.screen_feedback

import amobi.module.common.utils.DebugLogCustom
import amobi.module.common.utils.MixedUtils
import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.extentions.appClickable
import amobi.module.compose.foundation.AppButton
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppIcon
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.foundation.AppTextAutoSize
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontFamily
import amobi.module.compose.theme.AppFontSize
import amobi.module.compose.theme.AppThemeWrapper
import android.content.res.Configuration
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.presentation.common.shared_components.AppAppbar
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.NavigatorViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

// Constants for colors, dimensions and other values
private object FeedbackConstants {
    // Colors
    val PRIMARY_COLOR = Color(0xFF0ABAB5)
    val PRIMARY_COLOR_LIGHT = Color(0xFFE0F7FA)
    val TEXT_DARK = Color(0xFF132537)

    // Dimensions - Theo chuẩn Material Design của Google
    val CORNER_RADIUS = 8.dp  // Bo góc nhất quán cho tất cả các thành phần
    val STANDARD_PADDING = 16.dp  // Khoảng cách tiêu chuẩn giữa các thành phần chính
    val SMALL_PADDING = 12.dp  // Khoảng cách nhỏ hơn giữa các thành phần liên quan
    val TINY_PADDING = 4.dp  // Khoảng cách nhỏ nhất giữa các thành phần gần nhau
    val MEDIUM_PADDING = 8.dp  // Khoảng cách trung bình

    // Other constants
    const val MAX_CHAR_COUNT = 3000
    const val MAX_FILE_SIZE_MB = 25L
    const val UPLOAD_SIMULATION_DELAY = 300L
    const val SEND_FEEDBACK_DELAY = 2000L
}

data class FeedbackAttachment(
    val id: String = java.util.UUID.randomUUID().toString(),
    val uri: Uri,
    val name: String,
    val status: FileStatus = FileStatus.UPLOADING,
    val errorMessage: String? = null,
    val progress: Float = 0f,
    val size: Long = 0L,
    val cancelled: Boolean = false
)

enum class FileStatus {
    UPLOADING, SUCCESS, ERROR
}

/**
 * Simulates file upload process with progress updates
 * @param onComplete Callback with boolean indicating success (true) or failure (false)
 */
private fun simulateFileUpload(
    context: android.content.Context,
    attachmentId: String,
    attachments: MutableList<FeedbackAttachment>,
    successProbability: Double = 0.7,
    onComplete: ((Boolean) -> Unit)? = null
) {
    var currentProgress = 0f
    val handler = android.os.Handler()

    val progressUpdater = object : Runnable {
        override fun run() {
            // Find attachment by ID
            val currentIndex = attachments.indexOfFirst { it.id == attachmentId }
            if (currentIndex >= 0) {
                val currentAttachment = attachments[currentIndex]

                // Check if cancelled
                if (!currentAttachment.cancelled && currentProgress < 1f) {
                    // Update progress
                    currentProgress =
                        (currentProgress + (0.05f + Math.random() * 0.1f)).coerceAtMost(1.0)
                            .toFloat()
                    attachments[currentIndex] = currentAttachment.copy(progress = currentProgress)

                    // Continue if not complete
                    if (currentProgress < 1f) {
                        handler.postDelayed(this, FeedbackConstants.UPLOAD_SIMULATION_DELAY)
                    } else {
                        // Set final status
                        val isSuccess = Math.random() <= successProbability
                        val status = if (!isSuccess) FileStatus.ERROR else FileStatus.SUCCESS
                        val errorMsg =
                            if (status == FileStatus.ERROR) context.getString(R.string.feedback_upload_failed) else null
                        attachments[currentIndex] =
                            currentAttachment.copy(status = status, errorMessage = errorMsg)

                        // Call the completion callback with success status
                        onComplete?.invoke(isSuccess)
                    }
                }
            }
        }
    }

    handler.post(progressUpdater)
}

@AppPreview
@Composable
private fun FeedbackSuggestionScreenComposePreview(
    uiMode: Int = Configuration.UI_MODE_NIGHT_NO
) {
    PreviewAssist.initVariables(LocalContext.current)
    AppThemeWrapper {
        FeedbackSuggestionScreenCompose()
    }
}

/**
 * Creates a FeedbackAttachment from a Uri if it meets the requirements
 * Returns null if the file doesn't meet requirements
 */
private fun createAttachmentFromUri(
    context: android.content.Context,
    uri: Uri
): FeedbackAttachment? {
    // Get file name and size
    val fileName = uri.lastPathSegment?.substringAfterLast('/') ?: "Unknown file"
    val fileSize = try {
        context.contentResolver.openFileDescriptor(uri, "r")?.use { it.statSize } ?: 0L
    } catch (e: Exception) {
        0L
    }

    // Check file size limit
    val maxSizeBytes = FeedbackConstants.MAX_FILE_SIZE_MB * 1024 * 1024
    if (fileSize > maxSizeBytes) {
        // Show toast message for large file
        MixedUtils.showToast(
            context,
            context.getString(
                R.string.feedback_attachment_too_large,
                FeedbackConstants.MAX_FILE_SIZE_MB
            )
        )
        return null
    }

    // Check MIME type to ensure it's PDF or image
    val mimeType = context.contentResolver.getType(uri)
    return if (mimeType?.startsWith("image/") == true || mimeType == "application/pdf") {
        // Create attachment with unique ID
        FeedbackAttachment(uri = uri, name = fileName, size = fileSize)
    } else {
        MixedUtils.showToast(context, context.getString(R.string.feedback_attachment_type_error))
        null
    }
}

@Composable
fun FeedbackSuggestionScreenCompose() {
    val context = LocalContext.current

    var isNavigatingBack by remember { mutableStateOf(false) }
    var isSending by remember { mutableStateOf(false) }

    val navigatorViewModel = NavigatorViewModel.getInstance()

    // Use the FeedbackScreenViewModel
    val viewModel: FeedbackScreenViewModel = hiltViewModel()

    // Observe state flows
    val isLoading by viewModel.isLoading.collectAsState()
    val isMessageEmpty by viewModel.isMessageEmpty.collectAsState()

    // Get values from ViewModel
    val feedbackTitle = viewModel.feedbackTitle.value
    val feedbackText = viewModel.feedbackMessage.value

    // Calculate character count
    val charCount = remember(feedbackText) {
        feedbackText.length
    }

    val gradientBrush = Brush.linearGradient(
        colors = listOf(Color(0xFF81D8D0), Color(0xFF0ABAB5)), // Gradient màu mong muốn
        start = Offset(0f, 0f),
        end = Offset(0f, 100f)
    )

    val coroutineScope = rememberCoroutineScope()

    // Local attachments list for UI display
    val attachments = remember { mutableStateListOf<FeedbackAttachment>() }

    val errorColor = AppColors.current.textError

    // Common TextField colors for reuse
    val textFieldColors = TextFieldDefaults.colors(
        focusedTextColor = AppColors.current.text,
        unfocusedTextColor = AppColors.current.text,
        focusedIndicatorColor = Color.Transparent,
        unfocusedIndicatorColor = Color.Transparent,
        focusedContainerColor = AppColors.current.backgroundInputChatBotCompose,
        unfocusedContainerColor = AppColors.current.backgroundInputChatBotCompose
    )

    val pickFileLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            // Create attachment if valid
            createAttachmentFromUri(context, uri)?.let { newAttachment ->
                attachments.add(newAttachment)
                // Start upload simulation
                simulateFileUpload(context, newAttachment.id, attachments, onComplete = { success ->
                    if (success) {
                        // Only add to ViewModel if upload was successful
                        // Instead of using viewModel.addAttachment which would validate again,
                        // directly add the URI to the ViewModel's list
                        viewModel.uriList.add(uri)

                        // Update ViewModel's tracking variables
                        val size = newAttachment.size
                        val displayName = newAttachment.name
                        val displaySize = formatFileSize(size)

                        // Update attachment info text in ViewModel
                        val currentText =
                            if (viewModel.attachmentInfoText.value.isNotEmpty()) "${viewModel.attachmentInfoText.value}\n" else ""
                        viewModel.attachmentInfoText.value =
                            "$currentText$displayName ($displaySize)"
                    } else {
                        // If upload failed, don't add to ViewModel
                        DebugLogCustom.logd("Upload failed for ${newAttachment.name}, not adding to ViewModel")
                    }
                })
            }
        }
    }
    Scaffold(containerColor = Color.Transparent) { innerPadding ->
        AppColumn(
            modifier = Modifier
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // App Bar
            AppAppbar(
                innerPadding = innerPadding,
                title = stringResource(R.string.txtid_feedback),
                onBack = {
                    if (!isNavigatingBack) {
                        isNavigatingBack = true
                        navigatorViewModel.navigateBack()
                    }
                },
                color = AppColors.current.text
            )

            // Main Content
            AppColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(horizontal = FeedbackConstants.STANDARD_PADDING),
            ) {

                AppSpacer(FeedbackConstants.STANDARD_PADDING)
                // Header
                AppText(
                    text = stringResource(R.string.feedback_header),
                    color = AppColors.current.titleText,
                    fontSize = AppFontSize.BODY1,
                    fontWeight = FontWeight.W700,
                    textAlign = TextAlign.Start,
                    lineHeight = 24.sp,
                    modifier = Modifier.fillMaxWidth()
                )
                AppSpacer(FeedbackConstants.TINY_PADDING)

                // Subheader
                AppText(
                    text = stringResource(R.string.feedback_subheader),
                    color = AppColors.current.text,
                    fontSize = AppFontSize.BODY2,
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Start,
                    lineHeight = 20.sp,
                    maxLines = 2,
                    modifier = Modifier.fillMaxWidth()
                )

                // Title TextField
                TextField(
                    value = feedbackTitle,
                    onValueChange = { viewModel.feedbackTitle.value = it },
                    placeholder = {
                        AppText(
                            text = stringResource(R.string.feedback_title_hint),
                            color = AppColors.current.textHintColor,
                            fontSize = AppFontSize.BODY2,
                            fontWeight = FontWeight.W400,
                            textAlign = TextAlign.Start,
                            lineHeight = 20.sp,
                            maxLines = 2,
                            modifier = Modifier.fillMaxWidth()
                        )
                    },
                    colors = textFieldColors,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = FeedbackConstants.STANDARD_PADDING)
                        .clip(RoundedCornerShape(FeedbackConstants.CORNER_RADIUS))
                        .background(
                            AppColors.current.backgroundInputChatBotCompose,
                            shape = RoundedCornerShape(FeedbackConstants.CORNER_RADIUS)
                        ),
                )

                AppSpacer(FeedbackConstants.SMALL_PADDING)

                // Feedback TextField
                TextField(
                    value = feedbackText,
                    onValueChange = { newText ->
                        // Only accept text if under max character count
                        if (newText.length <= FeedbackConstants.MAX_CHAR_COUNT || newText.length < feedbackText.length) {
                            viewModel.feedbackMessage.value = newText
                            // charCount is calculated in remember block
                            viewModel.clearMessageError()
                        }
                    },
                    placeholder = {
                        val placeholderText = stringResource(R.string.feedback_message_hint)
                        val textWithoutAsterisk = placeholderText.substringBefore(" *")

                        AppRow(modifier = Modifier.fillMaxWidth()) {
                            AppText(
                                text = textWithoutAsterisk,
                                color = if (isMessageEmpty)
                                    errorColor.copy(alpha = 0.6f)
                                else
                                    AppColors.current.textHintColor,
                                fontSize = AppFontSize.BODY2,
                                fontWeight = FontWeight.W400,
                                textAlign = TextAlign.Start,
                                lineHeight = 20.sp,
                                maxLines = 2
                            )
                            AppText(
                                text = " *",
                                color = errorColor,
                                fontSize = AppFontSize.BODY2,
                                fontWeight = FontWeight.W400,
                                textAlign = TextAlign.Start,
                                lineHeight = 20.sp
                            )
                        }
                    },
                    colors = textFieldColors,
                    supportingText = {
                        AppText(
                            text = stringResource(
                                R.string.feedback_word_count,
                                charCount,
                                FeedbackConstants.MAX_CHAR_COUNT
                            ),
                            fontSize = AppFontSize.BODY2,
                            fontWeight = FontWeight.Normal,
                            color = AppColors.current.textHintColor,
                            modifier = Modifier.fillMaxWidth(),
                            textAlign = TextAlign.End
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(136.dp)
                        .clip(RoundedCornerShape(FeedbackConstants.CORNER_RADIUS))
                        .background(
                            AppColors.current.backgroundInputChatBotCompose,
                            shape = RoundedCornerShape(FeedbackConstants.CORNER_RADIUS)
                        )
                        .then(
                            if (isMessageEmpty)
                                Modifier.border(
                                    0.5.dp,
                                    errorColor,
                                    shape = RoundedCornerShape(FeedbackConstants.CORNER_RADIUS)
                                )
                            else
                                Modifier
                        ),
                )

                AppSpacer(FeedbackConstants.SMALL_PADDING)

                // File Attachment Button
                AppRow(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(FeedbackConstants.CORNER_RADIUS))
                        .background(
                            color = FeedbackConstants.PRIMARY_COLOR_LIGHT,
                            shape = RoundedCornerShape(FeedbackConstants.CORNER_RADIUS)
                        )
                        .appClickable {
                            pickFileLauncher.launch("*/*")
                        }
                        .dashedBorder(
                            1.dp,
                            FeedbackConstants.PRIMARY_COLOR,
                            FeedbackConstants.CORNER_RADIUS
                        )
                        .padding(FeedbackConstants.STANDARD_PADDING),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AppIcon(
                        icon = R.drawable.ic_choose_file,
                        tint = null
                    )
                    AppSpacer(FeedbackConstants.SMALL_PADDING)
                    val normalText = stringResource(
                        R.string.feedback_attachment_info_new,
                        FeedbackConstants.MAX_FILE_SIZE_MB.toInt()
                    ) + ", "
                    val highlightText = stringResource(R.string.choose_file)

                    val annotatedText = buildAnnotatedString {
                        append(normalText)
                        withStyle(style = SpanStyle(color = AppColors.current.buttonColor)) { // hoặc màu xanh bạn muốn
                            append(highlightText)
                        }
                    }

                    Text(
                        text = annotatedText,
                        fontSize = AppFontSize.BODY2,
                        fontWeight = FontWeight.W400,
                        color = FeedbackConstants.TEXT_DARK,
                        lineHeight = 20.sp,
                        fontFamily = AppFontFamily.get()
                    )

                }

                // Show attachments if any
                if (attachments.isNotEmpty()) {
                    AppSpacer(FeedbackConstants.STANDARD_PADDING)
                    AppText(
                        text = stringResource(R.string.feedback_attachments_header),
                        color = AppColors.current.titleText,
                        fontSize = AppFontSize.BODY2,
                        fontWeight = FontWeight.W600,
                        textAlign = TextAlign.Start,
                        modifier = Modifier.fillMaxWidth()
                    )
                    AppSpacer(FeedbackConstants.MEDIUM_PADDING)

                    LazyColumn(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        items(attachments) { attachment ->
                            FileAttachmentItem(
                                attachment = attachment,
                                onRemove = {
                                    attachments.remove(attachment)
                                    // Also remove from ViewModel
                                    val index = viewModel.uriList.indexOf(attachment.uri)
                                    if (index >= 0) {
                                        viewModel.uriList.removeAt(index)
                                    }
                                },
                                onRetry = { failedAttachment ->
                                    // Use ID to find attachment
                                    val index =
                                        attachments.indexOfFirst { it.id == failedAttachment.id }
                                    if (index >= 0) {
                                        // Reset attachment
                                        attachments[index] = attachments[index].copy(
                                            status = FileStatus.UPLOADING,
                                            progress = 0f,
                                            errorMessage = null,
                                            cancelled = false
                                        )

                                        // Start upload simulation with higher success probability
                                        simulateFileUpload(
                                            context,
                                            failedAttachment.id,
                                            attachments,
                                            0.9,
                                            onComplete = { success ->
                                                if (success) {
                                                    // Add to ViewModel on successful retry
                                                    // Instead of using viewModel.addAttachment which would validate again,
                                                    // directly add the URI to the ViewModel's list
                                                    viewModel.uriList.add(failedAttachment.uri)

                                                    // Update ViewModel's tracking variables
                                                    val size = failedAttachment.size
                                                    val displayName = failedAttachment.name
                                                    val displaySize = formatFileSize(size)

                                                    // Update attachment info text in ViewModel
                                                    val currentText =
                                                        if (viewModel.attachmentInfoText.value.isNotEmpty()) "${viewModel.attachmentInfoText.value}\n" else ""
                                                    viewModel.attachmentInfoText.value =
                                                        "$currentText$displayName ($displaySize)"
                                                }
                                            })
                                    }
                                },
                            )
                        }
                    }
                }
            }

            // Send Feedback Button and progress indicator
            AppColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = FeedbackConstants.STANDARD_PADDING,
                        vertical = FeedbackConstants.STANDARD_PADDING
                    )
                    .imePadding(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Show progress indicator when sending
                if (isLoading || isSending) {
                    LinearProgressIndicator(
                        modifier = Modifier.fillMaxWidth(),
                        color = FeedbackConstants.PRIMARY_COLOR
                    )
                    AppSpacer(FeedbackConstants.MEDIUM_PADDING)
                }

                // Send button
                val isEnabled = !isSending && !isLoading

                AppButton(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    onClick = {
                        // Kiểm tra nội dung bắt buộc trước khi gửi
                        if (!viewModel.validateFeedback()) {
                            // Hiển thị thông báo lỗi nếu nội dung bắt buộc chưa được nhập
                            MixedUtils.showToast(
                                context,
                                context.getString(R.string.feedback_message_empty_error)
                            )
                            return@AppButton
                        }

                        // Send feedback using ViewModel
                        isSending = true

                        coroutineScope.launch {
                            delay(1000)
                            isSending = false
                            viewModel.clearForm()
                            navigatorViewModel.navigateBack()
                            MixedUtils.showToast(
                                context,
                                context.getString(R.string.rate_me_thank_you_for_your_feedback)
                            )
                        }

//                            viewModel.sendFeedback(
//                                context,
//                                onSuccess = {
//                                    isSending = false
//                                    // Clear form data
//                                    viewModel.clearForm()
//                                    // Also clear local attachments
//                                    attachments.clear()
//                                    // Navigate back
//                                    navigatorViewModel.navigateBack()
//                                },
//                                onError = { errorMessage ->
//                                    isSending = false
//                                    MixedUtils.showToast(context, errorMessage)
//                                }
//                            )
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Transparent,
                        contentColor = AppColors.current.onText
                    ),
                    shape = RoundedCornerShape(8.dp),
                    enabled = isEnabled,
                    contentPadding = PaddingValues(0.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                brush = gradientBrush,
                                shape = RoundedCornerShape(8.dp)
                            )
                            .padding(horizontal = 16.dp, vertical = 12.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        AppTextAutoSize(
                            text = stringResource(R.string.feedback_send_button),
                            fontSize = AppFontSize.BODY1,
                            fontWeight = FontWeight.W500,
                            color = AppColors.current.onText,
                            lineHeight = 24.sp
                        )
                    }
                }
            }
        }
    }
}

/**
 * Extension function to add dashed border to a composable
 */
fun Modifier.dashedBorder(strokeWidth: Dp, color: Color, cornerRadiusDp: Dp) = composed {
    val density = LocalDensity.current
    val strokeWidthPx = density.run { strokeWidth.toPx() }
    val cornerRadiusPx = density.run { cornerRadiusDp.toPx() }

    this.then(
        Modifier.drawWithCache {
            onDrawBehind {
                val stroke = Stroke(
                    width = strokeWidthPx,
                    pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f)
                )

                drawRoundRect(
                    color = color,
                    style = stroke,
                    cornerRadius = CornerRadius(cornerRadiusPx)
                )
            }
        }
    )
}

/**
 * Displays a file attachment item with status indicators
 */
@Composable
fun FileAttachmentItem(
    attachment: FeedbackAttachment,
    onRemove: () -> Unit,
    onRetry: (FeedbackAttachment) -> Unit = {},
) {
    val errorColor = Color.Red

    AppRow(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = FeedbackConstants.TINY_PADDING)
            .clip(RoundedCornerShape(FeedbackConstants.CORNER_RADIUS))
            .background(
                AppColors.current.backgroundInputChatBotCompose,
                RoundedCornerShape(FeedbackConstants.CORNER_RADIUS)
            )
            .then(
                if (attachment.status == FileStatus.ERROR)
                    Modifier.border(
                        1.dp,
                        errorColor,
                        RoundedCornerShape(FeedbackConstants.CORNER_RADIUS)
                    )
                else
                    Modifier
            )
            .padding(FeedbackConstants.SMALL_PADDING),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // File icon
        AppIcon(
            icon = getFileIcon(attachment.name),
            tint = null,
            modifier = Modifier.size(24.dp)
        )

        AppSpacer(FeedbackConstants.MEDIUM_PADDING)

        // File details
        AppColumn(
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = FeedbackConstants.MEDIUM_PADDING)
        ) {
            // File name
            AppText(
                text = attachment.name,
                fontSize = AppFontSize.BODY2,
                fontWeight = FontWeight.W500,
                color = AppColors.current.titleText,
                maxLines = 1,
                modifier = Modifier.fillMaxWidth()
            )

            AppSpacer(FeedbackConstants.TINY_PADDING)

            // File size and status
            AppRow(verticalAlignment = Alignment.CenterVertically) {
                // Display file size
                AppText(
                    text = formatFileSize(attachment.size),
                    fontSize = AppFontSize.BODY2,
                    fontWeight = FontWeight.W400,
                    color = AppColors.current.textHintColor
                )
                AppSpacer(FeedbackConstants.MEDIUM_PADDING)

                // Status indicator
                when (attachment.status) {
                    FileStatus.UPLOADING -> {
                        CircularProgressIndicator(
                            modifier = Modifier.size(12.dp),
                            strokeWidth = 2.dp,
                            color = FeedbackConstants.PRIMARY_COLOR
                        )
                        AppSpacer(FeedbackConstants.TINY_PADDING)
                        AppText(
                            text = stringResource(R.string.feedback_uploading),
                            fontSize = AppFontSize.BODY2,
                            fontWeight = FontWeight.W400,
                            color = AppColors.current.textHintColor,
                            modifier = Modifier.padding(start = FeedbackConstants.TINY_PADDING)
                        )
                    }

                    FileStatus.SUCCESS -> {
                        AppIcon(
                            icon = R.drawable.ic_completed,
                            size = 12.dp,
                            tint = null
                        )
                        AppText(
                            text = stringResource(R.string.feedback_completed),
                            fontSize = AppFontSize.BODY2,
                            fontWeight = FontWeight.W400,
                            color = AppColors.current.textHintColor,
                            modifier = Modifier.padding(start = FeedbackConstants.TINY_PADDING)
                        )
                    }

                    FileStatus.ERROR -> { /* Error message shown below */
                    }
                }
            }

            // Error message or progress bar
            when {
                // Show error message
                attachment.status == FileStatus.ERROR && attachment.errorMessage != null -> {
                    AppRow(verticalAlignment = Alignment.CenterVertically) {
                        AppIcon(
                            icon = android.R.drawable.ic_dialog_alert,
                            tint = errorColor,
                            modifier = Modifier.size(12.dp)
                        )
                        AppSpacer(FeedbackConstants.TINY_PADDING)
                        AppText(
                            text = attachment.errorMessage,
                            fontSize = AppFontSize.BODY2,
                            fontWeight = FontWeight.W400,
                            color = errorColor,
                            modifier = Modifier.weight(1f)
                        )
                    }
                }
                // Show progress bar
                attachment.status == FileStatus.UPLOADING -> {
                    AppRow(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        LinearProgressIndicator(
                            progress = { attachment.progress },
                            modifier = Modifier
                                .weight(1f)
                                .height(4.dp),
                            color = FeedbackConstants.PRIMARY_COLOR,
                            trackColor = FeedbackConstants.PRIMARY_COLOR.copy(alpha = 0.2f)
                        )
                        AppSpacer(FeedbackConstants.MEDIUM_PADDING)
                        AppText(
                            text = "${(attachment.progress * 100).toInt()}%",
                            fontSize = AppFontSize.BODY2,
                            fontWeight = FontWeight.W400,
                            color = AppColors.current.textHintColor
                        )
                    }
                }
            }
        }

        // Action buttons
        if (attachment.status == FileStatus.ERROR && attachment.errorMessage != null) {
            AppIcon(
                icon = R.drawable.ic_retry,
                tint = null
            ) {
                onRetry(attachment)
            }
        }

        // Remove button - different icon based on status
        if (attachment.status == FileStatus.SUCCESS || attachment.status == FileStatus.ERROR) {
            AppIcon(
                icon = R.drawable.ic_history_trash,
                tint = AppColors.current.textError,
            ) {
                onRemove()
            }
        } else {
            AppIcon(
                icon = android.R.drawable.ic_menu_close_clear_cancel,
                tint = AppColors.current.text,
            ) {
                onRemove()
            }
        }
    }
}

/**
 * Formats file size in human-readable format
 */
private fun formatFileSize(size: Long): String {
    if (size <= 0) return "0 B"
    val units = arrayOf("B", "KB", "MB", "GB")
    val digitGroups = (Math.log10(size.toDouble()) / Math.log10(1024.0)).toInt()
    return String.format(
        "%.1f %s",
        size / Math.pow(1024.0, digitGroups.toDouble()),
        units[digitGroups]
    )
}

/**
 * Returns appropriate icon resource based on file extension
 */
private fun getFileIcon(fileName: String): Int {
    val extension = fileName.substringAfterLast('.', "").lowercase()

    return when (extension) {
        "pdf" -> R.drawable.ic_choose_file_pdf
        "doc", "docx" -> android.R.drawable.ic_menu_edit
        "xls", "xlsx" -> android.R.drawable.ic_menu_sort_by_size
        "jpg", "jpeg", "png", "gif", "bmp" -> R.drawable.ic_choose_file_png
        "mp4", "avi", "mov", "wmv" -> android.R.drawable.ic_menu_slideshow
        "mp3", "wav", "ogg", "flac" -> android.R.drawable.ic_lock_silent_mode_off
        "zip", "rar", "7z", "tar", "gz" -> android.R.drawable.ic_lock_silent_mode_off
        "txt" -> android.R.drawable.ic_dialog_info
        else -> R.drawable.ic_choose_file
    }
}