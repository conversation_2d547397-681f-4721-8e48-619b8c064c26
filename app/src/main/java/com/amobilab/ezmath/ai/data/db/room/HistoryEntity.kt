package com.amobilab.ezmath.ai.data.db.room

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "history_table")
data class HistoryEntity(
    @PrimaryKey(autoGenerate = true)
    val historyId: Int = 0,
    val timestamp: Long,
    val historyName: String,
    val isFavorite: Boolean,
    val content: String,
    val imageData: ByteArray?,
    @ColumnInfo(defaultValue = "")
    val questionMode: String,
    @ColumnInfo(defaultValue = "")
    val modelAiChat: String,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as HistoryEntity

        if (historyId != other.historyId) return false
        if (timestamp != other.timestamp) return false
        if (historyName != other.historyName) return false
        if (isFavorite != other.isFavorite) return false
        if (imageData != null) {
            if (other.imageData == null) return false
            if (!imageData.contentEquals(other.imageData)) return false
        } else if (other.imageData != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = historyId
        result = 31 * result + timestamp.hashCode()
        result = 31 * result + historyName.hashCode()
        result = 31 * result + isFavorite.hashCode()
        result = 31 * result + (imageData?.contentHashCode() ?: 0)
        return result
    }
}