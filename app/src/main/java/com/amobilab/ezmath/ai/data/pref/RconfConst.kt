package com.amobilab.ezmath.ai.data.pref

import amobi.module.common.configs.CommFigs

object RconfConst {
    fun getAbTestVersion(): String {
        if (!CommFigs.IS_SHOW_TEST_OPTION) return ""
        return ""
    }


    const val SHOW_AI_TYPE = "SHOW_AI_TYPE"
    const val SHOW_AI_BOTH_TYPE = 1
    const val SHOW_AI_GPT_TYPE = 2
    const val SHOW_AI_GEMINI_TYPE = 3

    const val IS_HIDE_SYSTEM_NAV_BAR = "IS_HIDE_SYSTEM_NAV_BAR"

    const val IS_SHOW_IAP = "IS_SHOW_IAP"
    const val NUM_COINS_PURCHASE_1500 = "NUM_COINS_PURCHASE_1500"
    const val NUM_COINS_PURCHASE_5500 = "NUM_COINS_PURCHASE_5500"
    const val NUM_COINS_PURCHASE_12000 = "NUM_COINS_PURCHASE_12000"
    const val NUM_COINS_PURCHASE_25000 = "NUM_COINS_PURCHASE_25000"
    const val NUM_COINS_PURCHASE_53000 = "NUM_COINS_PURCHASE_53000"

    const val TOKEN_ANSWER_TO_COIN_GEMINI = "TOKEN_ANSWER_TO_COIN_GEMINI"
    const val TOKEN_ANSWER_TO_COIN_GPT = "TOKEN_ANSWER_TO_COIN_GPT"
    const val TOKEN_PROMPT_TO_COIN_GEMINI = "TOKEN_PROMPT_TO_COIN_GEMINI"
    const val TOKEN_PROMPT_TO_COIN_GPT = "TOKEN_PROMPT_TO_COIN_GPT"

    const val CREDIT_SIGN_IN_REWARD = "CREDIT_SIGN_IN_REWARD"
    const val CREDIT_WATCH_REWARD_AD = "CREDIT_WATCH_REWARD_AD"

    const val SPLASH_TIME_OUT = "SPLASH_TIME_OUT"

    const val MODEL_GEMINI_NAME = "MODEL_GEMINI_NAME"
    const val MODEL_GPT_NAME = "MODEL_GPT_NAME"

    const val IS_USE_GATEWAY_API = "IS_USE_GATEWAY_API"
    const val IS_NOT_RANDOM_RESPONSE = "IS_NOT_RANDOM_RESPONSE"

    const val IS_AD_MULTI_IDS = "IS_AD_MULTI_IDS"
}