package com.amobilab.ezmath.ai.values

import amobi.module.common.configs.RconfAssist
import com.amobilab.ezmath.ai.data.pref.RconfConst

object Const {

    object AiModelName {
//        val GPT = "gpt-4o-mini"
//        val GEMINI = "gemini-1.5-flash"
//        val GEMINI = "gemini-2.0-flash-lite-preview-02-05"

        val GPT by lazy {
            RconfAssist.getString(RconfConst.MODEL_GPT_NAME)
        }
        val GEMINI by lazy {
            RconfAssist.getString(RconfConst.MODEL_GEMINI_NAME)
        }
    }

    object AiServiceName {
        const val GPT = "Chat GPT"
        const val GEMINI = "GEMINI"
        const val DEFAULT = "AI"
    }


    const val MAX_OUTPUT_TOKENS = 5000

    const val POLICY_URL = "https://amobilab.com/policy-ez-math.html"
}