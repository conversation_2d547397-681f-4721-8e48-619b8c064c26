package com.amobilab.ezmath.ai.data.db.powerSync

import android.content.Context
import com.amobilab.ezmath.ai.data.db.AppDatabase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch

/**
 * Ví dụ sử dụng UserRepository
 * Đây là file mẫu để minh họa cách sử dụng UserRepository
 */
class UserRepositoryExample(private val context: Context) {

    private val appDatabase = AppDatabase.getInstance()
    private val userRepository by lazy { appDatabase.getUserRepository() }

    init {
        // Khởi tạo database
        appDatabase.init(context)
    }

    // Ví dụ tạo user mới
    suspend fun createNewUser(initialCoin: Int = 100): String {
        val newUser = UserEntity(coin = initialCoin)
        return userRepository.insertUser(newUser)
    }

    // Ví dụ lấy thông tin user
    suspend fun getUserInfo(userId: String): UserEntity? {
        return userRepository.getUserById(userId)
    }

    // Ví dụ cập nhật coin cho user
    suspend fun updateUserCoin(userId: String, newCoinAmount: Int) {
        userRepository.updateUserCoin(userId, newCoinAmount)
    }

    // Ví dụ thêm coin cho user
    suspend fun addCoinToUser(userId: String, coinAmount: Int) {
        userRepository.addCoinToUser(userId, coinAmount)
    }

    // Ví dụ trừ coin từ user
    suspend fun spendCoin(userId: String, coinAmount: Int) {
        userRepository.subtractCoinFromUser(userId, coinAmount)
    }

    // Ví dụ theo dõi thay đổi user theo thời gian thực
    fun observeUser(userId: String) {
        CoroutineScope(Dispatchers.Main).launch {
            userRepository.watchUserById(userId).collect { user ->
                user?.let {
                    println("User coin updated: ${it.coin}")
                    // Cập nhật UI ở đây
                }
            }
        }
    }

    // Ví dụ theo dõi user đầu tiên (nếu chỉ có một user)
    fun observeFirstUser() {
        CoroutineScope(Dispatchers.Main).launch {
            userRepository.watchFirstUser().collect { user ->
                user?.let {
                    println("First user coin: ${it.coin}")
                    // Cập nhật UI ở đây
                }
            }
        }
    }

    // Ví dụ kiểm tra xem có user nào không
    suspend fun checkIfUserExists(): Boolean {
        return userRepository.hasAnyUser()
    }

    // Ví dụ lấy user đầu tiên
    suspend fun getFirstUser(): UserEntity? {
        return userRepository.getFirstUser()
    }

    // Ví dụ tạo user mặc định nếu chưa có
    suspend fun createDefaultUserIfNeeded(): UserEntity {
        val existingUser = userRepository.getFirstUser()
        return if (existingUser != null) {
            existingUser
        } else {
            val userId = createNewUser(initialCoin = 1000) // Tặng 1000 coin ban đầu
            userRepository.getUserById(userId)!!
        }
    }

    // Ví dụ xử lý giao dịch coin (kiểm tra số dư trước khi trừ)
    suspend fun processTransaction(userId: String, amount: Int, isSpending: Boolean): Boolean {
        val user = userRepository.getUserById(userId) ?: return false
        
        return if (isSpending) {
            // Kiểm tra số dư trước khi trừ
            if (user.coin >= amount) {
                userRepository.subtractCoinFromUser(userId, amount)
                true
            } else {
                false // Không đủ coin
            }
        } else {
            // Thêm coin
            userRepository.addCoinToUser(userId, amount)
            true
        }
    }

    // Ví dụ lấy danh sách tất cả user
    suspend fun getAllUsers(): List<UserEntity> {
        return userRepository.getAllUsers()
    }

    // Ví dụ theo dõi tất cả user
    fun observeAllUsers() {
        CoroutineScope(Dispatchers.Main).launch {
            userRepository.watchAllUsers().collect { users ->
                println("Total users: ${users.size}")
                users.forEach { user ->
                    println("User ${user.id}: ${user.coin} coins")
                }
                // Cập nhật UI ở đây
            }
        }
    }
}
