package com.amobilab.ezmath.ai.presentation.common.shared_viewmodels

import amobi.module.compose.extentions.PreviewAssist
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewmodel.compose.LocalViewModelStoreOwner
import com.amobilab.ezmath.ai.presentation.navigation.MainComposeActivity
import javax.inject.Inject


class MainLogicViewModel @Inject constructor() : ViewModel() {
    companion object {
        const val TAG = "MainLogicViewModel"

        @Composable
        fun getInstance(): MainLogicViewModel {
            return hiltViewModel<MainLogicViewModel>(
                viewModelStoreOwner =
                    if (PreviewAssist.IS_PREVIEW)
                        checkNotNull(LocalViewModelStoreOwner.current)
                    else
                        LocalContext.current as MainComposeActivity,
            )
        }
    }

    private var _isShowBanner: MutableLiveData<Boolean> = MutableLiveData(false)
    var isShowBanner: LiveData<Boolean> = _isShowBanner
    fun setShowBanner(isShow: Boolean) {
        if (_isShowBanner.value == isShow) return
        _isShowBanner.value = isShow
    }
    fun isShowBanner(): Boolean {
        return _isShowBanner.value == true
    }

    private var _isShowSplash: MutableLiveData<Boolean> = MutableLiveData(true)
    var isShowSplash: LiveData<Boolean> = _isShowSplash

    fun setShowSplash(isShow: Boolean) {
        if (_isShowSplash.value == isShow) return
        _isShowSplash.value = isShow
    }

    private var _isShowGDPR: MutableLiveData<Boolean> = MutableLiveData(true)
    var isShowGDPR: LiveData<Boolean> = _isShowGDPR
    fun setShowGDPR(isShow: Boolean) {
        if (_isShowGDPR.value == isShow) return
        _isShowGDPR.value = isShow
    }

    fun isShowGDPR(): Boolean {
        return _isShowGDPR.value == true
    }
}
