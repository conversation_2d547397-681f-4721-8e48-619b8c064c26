package com.amobilab.ezmath.ai.data.db.room

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.PrimaryKey

@Entity(
    tableName = "chat_table",
    foreignKeys = [
        ForeignKey(
            entity = HistoryEntity::class,
            parentColumns = ["historyId"],
            childColumns = ["historyId"],
            onDelete = ForeignKey.CASCADE
        )
    ]
)
data class ChatEntity(
    @PrimaryKey(autoGenerate = true)
    val chatListId: Int = 0,
    val historyId: Long,
    val timestamp: Long,
    val content: String,
    val isHuman: Boolean,
    @ColumnInfo(defaultValue = "0")
    val isError: Boolean = false,
    val imageData: ByteArray?,
    val botName: String?,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ChatEntity

        if (chatListId != other.chatListId) return false
        if (historyId != other.historyId) return false
        if (timestamp != other.timestamp) return false
        if (content != other.content) return false
        if (isHuman != other.isHuman) return false
        if (isError != other.isError) return false
        if (botName != other.botName) return false
        if (imageData != null) {
            if (other.imageData == null) return false
            if (!imageData.contentEquals(other.imageData)) return false
        } else if (other.imageData != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = chatListId
        result = 31 * result + historyId.hashCode()
        result = 31 * result + timestamp.hashCode()
        result = 31 * result + content.hashCode()
        result = 31 * result + isHuman.hashCode()
        result = 31 * result + isError.hashCode()
        result = 31 * result + botName.hashCode()
        result = 31 * result + (imageData?.contentHashCode() ?: 0)
        return result
    }
}