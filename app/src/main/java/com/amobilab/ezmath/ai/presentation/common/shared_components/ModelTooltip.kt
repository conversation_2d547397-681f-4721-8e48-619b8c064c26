package com.amobilab.ezmath.ai.presentation.common.shared_components

import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppButton
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode

@Composable
fun ModelTooltip(
    modifier: Modifier = Modifier,
    onDismiss: () -> Unit
) {
    val gradientBrush = Brush.linearGradient(
        colors = listOf(Color(0xFF81D8D0), Color(0xFF0ABAB5)),
        start = Offset(0f, 0f),
        end = Offset(0f, 100f)
    )

    Box(
        modifier = modifier
            .padding(12.dp)
    ) {
        Column(horizontalAlignment = Alignment.Start) {
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy((-8).dp)
            ) {
                items(ModelAiMode.entries.size) { index ->
                    val model = ModelAiMode.entries[index]
                    Icon(
                        painter = painterResource(id = model.iconRes),
                        contentDescription = null,
                        modifier = Modifier
                            .size(24.dp)
                            .clip(CircleShape)
                            .zIndex((ModelAiMode.entries.size - index).toFloat()), // icon trái nằm trên icon phải
                        tint = Color.Unspecified
                    )
                }
            }
            AppSpacer(8.dp)
            AppText(
                text = stringResource(R.string.multiple_ai_models_available),
                fontWeight = FontWeight.W700,
                fontSize = AppFontSize.BODY2,
                color = AppColors.current.text,//Color
                lineHeight = 20.sp
            )
            AppSpacer(8.dp)
            AppText(
                text = stringResource(R.string.tap_here_to_pick_the_one_that_best_suits_your_task),
                fontWeight = FontWeight.W400,
                fontSize = AppFontSize.BODY2,
                color = AppColors.current.titleContent,//Color
                lineHeight = 20.sp
            )
            AppSpacer(8.dp)
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.CenterEnd
            ) {
                AppButton(
                    modifier = Modifier
                        .height(32.dp)
                        .background(
                            brush = gradientBrush,
                            shape = RoundedCornerShape(8.dp)
                        ),
                    onClick = {
                        onDismiss()
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Transparent,
                        contentColor = AppColors.current.onText
                    ),
                    shape = RoundedCornerShape(8.dp),
                    enabled = true,
                    contentPadding = PaddingValues(0.dp)
                ) {
                    AppBox(
                        modifier = Modifier.padding(horizontal = 16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        AppText(
                            text = stringResource(R.string.ok_got_it),
                            fontSize = AppFontSize.SMALL,
                            fontWeight = FontWeight.W700,
                            color = AppColors.current.onText,
                            lineHeight = 16.sp
                        )
                    }
                }
            }
        }
    }
}
