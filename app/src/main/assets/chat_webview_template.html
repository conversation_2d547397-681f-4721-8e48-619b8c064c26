<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="file:///android_asset/katex/katex.css">
    <style>
        @font-face {
            font-family: 'MyFont';
            src: url('file:///android_asset/fonts/manrope_regular.ttf');
        }
        body { 
            font-family: 'MyFont'; 
            color: ${textColor};
            padding: 0 16px;
            margin: 0;
        }
        img {
            max-width: 100%;
            height: auto;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
            pointer-events: auto;
        }
        a { color: {{LINK_COLOR}}; }
        p code, td code {
            background-color: {{CODE_BACKGROUND}};
            padding: 4px 4px 2px 4px;
            margin: 4px;
            border-radius: 4px;
            font-family: monospace;
            font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
        }
        pre {
            background-color: {{PRE_BACKGROUND}};
            display: block;
            padding: 16px;
            overflow-x: auto;
            margin: 16px 0;
        }
        blockquote {
            border-left: 4px solid {{QUOTE_BACKGROUND}};
            padding: 0;
            margin: 16px 0;
        }
        blockquote > * { margin-left: 16px; padding: 0; }
        blockquote blockquote { margin: 16px; }
        table {
            border-collapse: collapse;
            display: block;
            overflow-x: auto;
            margin: 16px 0;
        }
        th, td {
            border: 1px solid {{BORDER_COLOR}};
            padding: 6px 13px;
            line-height: 1.5;
        }
        tr:nth-child(even) { background-color: {{PRE_BACKGROUND}}; }
        video::-webkit-media-controls-fullscreen-button { display: none !important; }
        video, audio { width: 100%; }

      /* Alert Styles */
        .markdown-alert {
            padding: 0;
            margin: 16px 0;
            border-left: 4px solid;
        }

        .markdown-alert p {
            margin-left: 16px;
            margin-right: 16px;
            color: inherit; /* Changed from #444 to inherit body text color */
        }

        .markdown-alert p:first-of-type {
            margin-top: 4px;
            padding-top: 0;
        }

        .markdown-alert p:last-of-type {
            margin-bottom: 0;
            padding-bottom: 16px;
        }

        .markdown-alert-title {
            font-weight: 500;
            display: flex;
            align-items: center;
            padding: 8px 16px;
        }

        .markdown-alert-title svg {
            margin-right: 8px;
        }

        .markdown-alert-title svg path {
            fill: currentColor;
        }

        /* Alert title colors */
        .markdown-alert.note .markdown-alert-title {
            color: #4493f8;
        }

        .markdown-alert.tip .markdown-alert-title {
            color: #3fb950;
        }

        .markdown-alert.important .markdown-alert-title {
            color: #ab7df8;
        }

        .markdown-alert.warning .markdown-alert-title {
            color: #d29922;
        }

        .markdown-alert.caution .markdown-alert-title {
            color: #f85149;
        }

        /* Border colors */
        .markdown-alert.note {
            border-color: #4493f8;
        }

        .markdown-alert.tip {
            border-color: #3fb950;
        }

        .markdown-alert.important {
            border-color: #ab7df8;
        }

        .markdown-alert.warning {
            border-color: #d29922;
        }

        .markdown-alert.caution {
            border-color: #f85149;
        }
    </style>
</head>
<script>
    function renderInitial() {
        renderMathInElement(document.body, {
            delimiters: [
              {left: "\\[", right: "\\]", display: true},
              {left: "\\(", right: "\\)", display: false},
              {left: "$$", right: "$$", display: true},
              {left: "$", right: "$", display: false},
              {left: "\\begin{equation}", right: "\\end{equation}", display: true},
              {left: "\\begin{align}", right: "\\end{align}", display: true},
              {left: "\\begin{alignat}", right: "\\end{alignat}", display: true},
              {left: "\\begin{gather}", right: "\\end{gather}", display: true},
              {left: "\\begin{CD}", right: "\\end{CD}", display: true},
            ],
            throwOnError: false,
            strict: false
        });
    };

// Khởi tạo các đối tượng xử lý
const handlers = {
    // Hàm chính để xử lý tất cả các loại phương tiện trong tài liệu
    processMediaItems: () => {
        handlers.processImages(); // Xử lý hình ảnh
        handlers.processAudio(); // Xử lý âm thanh
        handlers.processVideos(); // Xử lý video
        handlers.processCheckboxLists(); // Xử lý danh sách checkbox
        handlers.processAlerts(); // Xử lý thông báo cảnh báo
    },

    // Xử lý tất cả các thẻ hình ảnh trong tài liệu
    processImages: () => {
        document.querySelectorAll('img').forEach((img, index) => {
            const imageName = img.getAttribute('src'); // Lấy đường dẫn hình ảnh
            const id = 'img_' + index; // Tạo ID duy nhất cho hình ảnh
            img.setAttribute('data-id', id); // Gán ID vào thuộc tính data-id
            img.setAttribute('loading', 'lazy'); // Thêm lazy loading để tối ưu hiệu suất
            // Gọi phương thức native để xử lý đường dẫn phương tiện
            window.mediaPathHandler.processMedia(imageName, id, "image");

            let touchStartTime; // Biến lưu thời điểm bắt đầu chạm

            // Xử lý sự kiện click để mở hình ảnh
            img.onclick = () => window.imageInterface.onImageClick(img.src);
            // Vô hiệu hóa menu chuột phải trên hình ảnh
            img.oncontextmenu = e => { e.preventDefault(); return false; };
            // Vô hiệu hóa khả năng kéo thả hình ảnh
            img.draggable = false;

            // Ghi lại thời điểm bắt đầu chạm (cho thiết bị cảm ứng)
            img.addEventListener('touchstart', () => {
                touchStartTime = Date.now();
            });

            // Xử lý sự kiện kết thúc chạm, phát hiện chạm giữ lâu (long press)
            img.addEventListener('touchend', e => {
                if (Date.now() - touchStartTime >= 500) { // Nếu chạm giữ lâu hơn 500ms
                    e.preventDefault(); // Ngăn chặn hành động mặc định
                }
            });
        });
    },

    // Xử lý tất cả các thẻ âm thanh trong tài liệu
    processAudio: () => {
        document.querySelectorAll('audio').forEach((audio, index) => {
            const audioName = audio.getAttribute('src'); // Lấy đường dẫn âm thanh
            const id = 'audio_' + index; // Tạo ID duy nhất cho âm thanh
            audio.setAttribute('data-id', id); // Gán ID vào thuộc tính data-id
            audio.controls = true; // Hiển thị các điều khiển âm thanh
            audio.controlsList = "nodownload"; // Vô hiệu hóa tùy chọn tải xuống
            // Gọi phương thức native để xử lý đường dẫn phương tiện
            window.mediaPathHandler.processMedia(audioName, id, "audio");

            // Vô hiệu hóa menu chuột phải trên âm thanh
            audio.oncontextmenu = e => { e.preventDefault(); return false; };
        });
    },

    // Xử lý tất cả các thẻ video trong tài liệu
    processVideos: () => {
        document.querySelectorAll('video').forEach((video, index) => {
            const videoName = video.getAttribute('src'); // Lấy đường dẫn video
            const id = 'video_' + index; // Tạo ID duy nhất cho video
            video.setAttribute('data-id', id); // Gán ID vào thuộc tính data-id
            video.controls = true; // Hiển thị các điều khiển video
            video.controlsList = "nodownload nofullscreen"; // Vô hiệu hóa tải xuống và toàn màn hình
            // Gọi phương thức native để xử lý đường dẫn phương tiện
            window.mediaPathHandler.processMedia(videoName, id, "video");

            // Vô hiệu hóa menu chuột phải trên video
            video.oncontextmenu = e => { e.preventDefault(); return false; };
        });
    },

    // Xử lý danh sách có checkbox
    processCheckboxLists: () => {
        document.querySelectorAll('li').forEach(li => {
            // Nếu mục danh sách chứa checkbox
            if (li.querySelector('input[type="checkbox"]')) {
                li.style.listStyleType = 'none'; // Loại bỏ dấu đầu dòng mặc định
            }
        });
    },

    // Xử lý các thông báo cảnh báo (alerts) từ cú pháp blockquote đặc biệt
    processAlerts: () => {
        document.querySelectorAll('blockquote').forEach(blockquote => {
            const firstParagraph = blockquote.querySelector('p:first-child');
            if (!firstParagraph) return; // Nếu không có đoạn văn đầu tiên, bỏ qua

            // 1. Kiểm tra xem nút con đầu tiên của đoạn văn có phải là nút văn bản không
            let firstChildNode = firstParagraph.firstChild;
            let alertMatch = null;

            if (firstChildNode && firstChildNode.nodeType === Node.TEXT_NODE) {
                // 2. Tìm kiếm mẫu cảnh báo trong nút văn bản đầu tiên
                //    Lưu ý: \s* khớp với khoảng trắng hoặc ký tự xuống dòng sau thẻ
                alertMatch = firstChildNode.nodeValue.match(/^\[!(NOTE|TIP|IMPORTANT|WARNING|CAUTION)\]\s*/i);
            }

            // Nếu nút đầu tiên không phải là nút văn bản, hoặc không khớp với mẫu, thoát
            if (!alertMatch) return;

            // Định nghĩa các đường dẫn SVG cho biểu tượng của từng loại cảnh báo
            const iconPaths = {
                note: 'M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8Zm8-6.5a6.5 6.5 0 1 0 0 13 6.5 6.5 0 0 0 0-13ZM6.5 7.75A.75.75 0 0 1 7.25 7h1a.75.75 0 0 1 .75.75v2.75h.25a.75.75 0 0 1 0 1.5h-2a.75.75 0 0 1 0-1.5h.25v-2h-.25a.75.75 0 0 1-.75-.75ZM8 6a1 1 0 1 1 0-2 1 1 0 0 1 0 2Z',
                tip: 'M8 1.5c-2.363 0-4 1.69-4 3.75 0 .984.424 1.625.984 2.304l.214.253c.223.264.47.556.673.848.284.411.537.896.621 1.49a.75.75 0 0 1-1.484.211c-.04-.282-.163-.547-.37-.847a8.456 8.456 0 0 0-.542-.68c-.084-.1-.173-.205-.268-.32C3.201 7.75 2.5 6.766 2.5 5.25 2.5 2.31 4.863 0 8 0s5.5 2.31 5.5 5.25c0 1.516-.701 2.5-1.328 3.259-.095.115-.184.22-.268.319-.207.245-.383.453-.541.681-.208.3-.33.565-.37.847a.751.751 0 0 1-1.485-.212c.084-.593.337-1.078.621-1.489.203-.292.45-.584.673-.848.075-.088.147-.173.213-.253.561-.679.985-1.32.985-2.304 0-2.06-1.637-3.75-4-3.75ZM5.75 12h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1 0-1.5ZM6 15.25a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 0 1.5h-2.5a.75.75 0 0 1-.75-.75Z',
                important: 'M0 1.75C0 .784.784 0 1.75 0h12.5C15.216 0 16 .784 16 1.75v9.5A1.75 1.75 0 0 1 14.25 13H8.06l-2.573 2.573A1.458 1.458 0 0 1 3 14.543V13H1.75A1.75 1.75 0 0 1 0 11.25Zm1.75-.25a.25.25 0 0 0-.25.25v9.5c0 .*************.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-9.5a.25.25 0 0 0-.25-.25Zm7 2.25v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 9a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z',
                warning: 'M6.457 1.047c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0 1 14.082 15H1.918a1.75 1.75 0 0 1-1.543-2.575Zm1.763.707a.25.25 0 0 0-.44 0L1.698 13.132a.25.25 0 0 0 .22.368h12.164a.25.25 0 0 0 .22-.368Zm.53 3.996v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 11a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z',
                caution: 'M4.47.22A.749.749 0 0 1 5 0h6c.199 0 .389.079.53.22l4.25 4.25c.141.14.22.331.22.53v6a.749.749 0 0 1-.22.53l-4.25 4.25A.749.749 0 0 1 11 16H5a.749.749 0 0 1-.53-.22L.22 11.53A.749.749 0 0 1 0 11V5c0-.199.079-.389.22-.53Zm.84 1.28L1.5 5.31v5.38l3.81 3.81h5.38l3.81-3.81V5.31L10.69 1.5ZM8 4a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5A.75.75 0 0 1 8 4Zm0 8a1 1 0 1 1 0-2 1 1 0 0 1 0 2Z'
            };

            const alertType = alertMatch[1].toLowerCase(); // Lấy loại cảnh báo (note, tip, v.v.)
            const alertTitle = alertType.charAt(0).toUpperCase() + alertType.slice(1); // Viết hoa chữ cái đầu

            // Tạo container cho cảnh báo
            const alertDiv = document.createElement('div');
            alertDiv.className = `markdown-alert ${alertType}`;

            // Tạo phần tử tiêu đề
            const titleDiv = document.createElement('div');
            titleDiv.className = 'markdown-alert-title';
            if (iconPaths[alertType]) {
                // Tạo biểu tượng SVG cho loại cảnh báo
                const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                svg.setAttribute('viewBox', '0 0 16 16');
                svg.setAttribute('width', '16');
                svg.setAttribute('height', '16');
                svg.setAttribute('aria-hidden', 'true');
                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                path.setAttribute('d', iconPaths[alertType]);
                svg.appendChild(path);
                titleDiv.appendChild(svg);
            }
            const titleText = document.createTextNode(alertTitle);
            titleDiv.appendChild(titleText);
            alertDiv.appendChild(titleDiv);

            // 3. Lấy độ dài thực tế của chuỗi khớp (ví dụ: "[!NOTE]\n")
            const matchedLength = alertMatch[0].length;

            // 4. Sửa đổi giá trị của nút văn bản đầu tiên, loại bỏ phần khớp
            firstChildNode.nodeValue = firstChildNode.nodeValue.substring(matchedLength);

            // Nếu nút văn bản trở thành rỗng sau khi loại bỏ phần khớp, xóa nó
            if (firstChildNode.nodeValue.length === 0) {
                const nodeToRemove = firstChildNode;
                firstChildNode = firstChildNode.nextSibling; // Di chuyển đến nút anh em tiếp theo
                nodeToRemove.remove(); // Xóa nút rỗng
            }

            // 6. Kiểm tra xem nút tiếp theo có phải là <br> không, nếu có thì xóa
            //    Xử lý trường hợp trình render Markdown có thể chèn <br> sau thẻ
            if (firstChildNode && firstChildNode.nodeType === Node.ELEMENT_NODE && firstChildNode.tagName === 'BR') {
                const brToRemove = firstChildNode;
                firstChildNode = firstChildNode.nextSibling; // Di chuyển đến nút sau <br>
                brToRemove.remove();
            }

            // 7. Thực hiện trimStart() trên nút văn bản đầu tiên hiện tại (nếu có)
            //    Loại bỏ khoảng trắng đầu có thể còn lại sau khi xóa thẻ hoặc <br>
            if (firstChildNode && firstChildNode.nodeType === Node.TEXT_NODE) {
                firstChildNode.nodeValue = firstChildNode.nodeValue.trimStart();
                // Nếu sau khi trim thành rỗng, cũng xóa nó
                 if (firstChildNode.nodeValue.length === 0) {
                     firstChildNode.remove();
                     // Lưu ý: Không cần theo dõi nextSibling ở đây vì việc kiểm tra nội dung toàn bộ đoạn văn sẽ xử lý
                 }
            }

            // 8. Kiểm tra xem toàn bộ đoạn văn hiện có trống không (không có nút con hoặc tất cả nút con cộng lại có textContent trống)
            const paragraphIsEmpty = !firstParagraph.hasChildNodes() || firstParagraph.textContent.trim() === '';

            // 9. Di chuyển đoạn văn đã xử lý (nếu không trống) và tất cả nội dung còn lại trong blockquote vào alertDiv
            if (!paragraphIsEmpty) {
                alertDiv.appendChild(firstParagraph);
            } else {
                firstParagraph.remove(); // Nếu đoạn văn trống, xóa hoàn toàn
            }

            // Di chuyển bất kỳ nút con còn lại nào trong blockquote (ví dụ: trường hợp nhiều đoạn văn)
            while (blockquote.firstChild) {
                alertDiv.appendChild(blockquote.firstChild);
            }

            // Thay thế blockquote bằng cảnh báo tùy chỉnh của chúng ta
            blockquote.parentNode.replaceChild(alertDiv, blockquote);
        });
    }
};

// Thực thi hàm khởi tạo khi tài liệu đã tải xong
document.addEventListener('DOMContentLoaded', () => {
    // Xử lý tất cả các phương tiện và phần tử đặc biệt
    handlers.processMediaItems();

    // Khởi tạo KaTeX để render công thức toán học
    if (typeof renderMathInElement !== 'undefined') {
        renderMathInElement(document.body, {
            // Định nghĩa các cặp ký tự giới hạn để nhận diện công thức toán học
            delimiters: [
              {left: "\\[", right: "\\]", display: true},
              {left: "\\(", right: "\\)", display: false},
              {left: "$$", right: "$$", display: true},
              {left: "$", right: "$", display: false},
              {left: "\\begin{equation}", right: "\\end{equation}", display: true},
              {left: "\\begin{align}", right: "\\end{align}", display: true},
              {left: "\\begin{alignat}", right: "\\end{alignat}", display: true},
              {left: "\\begin{gather}", right: "\\end{gather}", display: true},
              {left: "\\begin{CD}", right: "\\end{CD}", display: true},
            ],
            throwOnError: false, // Không ném lỗi khi gặp lỗi cú pháp, chỉ hiển thị lỗi trong công thức
            strict: false
        });
    }
});
</script>
<body>
<script src="file:///android_asset/mermaid.js"></script>
<script src="file:///android_asset/katex/katex.js"></script>
<script src="file:///android_asset/katex/auto-render.js"></script>
<script src="file:///android_asset/katex/mathtex-script-type.js"></script>
</body>
</html>
