package com.amobilab.ezmath.ai.configs

import amobi.module.otp.OtpHelper
import android.content.Context
import android.util.Base64
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

object AppSecret {
    const val IS_DEV = true

    // Split the secret into 4 parts and obfuscate with simple encoding
    private val PART1 = "WjB1UERTaEQ="  // Base64 of "Z0uPDShD"
    private val PART2 = "OEhuMHJ0S3M="  // Base64 of "8Hn0rtKs"
    private val PART3 = "eG5tTHV0Wnc="  // Base64 of "xnmLutZw"
    private val PART4 = "WlI3U3NOV0g="  // Base64 of "ZR7SsNWH"

    // Encryption key parts
    private val KEY_PART1 = "8dK9"
    private val KEY_PART2 = "Lm3n"
    private val KEY_PART3 = "Px5q"
    private val KEY_PART4 = "Yw7z"

    // IV for encryption
    private val IV = byteArrayOf(
        0x12, 0x34, 0x56, 0x78,
        0x9A.toByte(), 0xBC.toByte(), 0xDE.toByte(), 0xF0.toByte(),
        0x12, 0x34, 0x56, 0x78,
        0x9A.toByte(), 0xBC.toByte(), 0xDE.toByte(), 0xF0.toByte()
    )

    private fun assembleSecret(): String {
        val part1 = String(Base64.decode(PART1, Base64.DEFAULT))
        val part2 = String(Base64.decode(PART2, Base64.DEFAULT))
        val part3 = String(Base64.decode(PART3, Base64.DEFAULT))
        val part4 = String(Base64.decode(PART4, Base64.DEFAULT))
        return part1 + part2 + part3 + part4
    }

    private fun getEncryptionKey(): SecretKeySpec {
        val key = (KEY_PART1 + KEY_PART2 + KEY_PART3 + KEY_PART4).toByteArray()
        return SecretKeySpec(key, "AES")
    }

    private fun getIV(): IvParameterSpec {
        return IvParameterSpec(IV)
    }

    private fun encrypt(strToEncrypt: String): String {
        val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
        cipher.init(Cipher.ENCRYPT_MODE, getEncryptionKey(), getIV())
        val encrypted = cipher.doFinal(strToEncrypt.toByteArray())
        return Base64.encodeToString(encrypted, Base64.NO_WRAP)
    }

    fun initializeSecret(context: Context) {
        val secret = assembleSecret()
        // Save the secret directly without additional encryption
        OtpHelper.setSecret(context, secret)
    }
} 