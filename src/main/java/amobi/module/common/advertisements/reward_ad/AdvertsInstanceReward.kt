package amobi.module.common.advertisements.reward_ad

import amobi.module.common.CommApplication
import amobi.module.common.advertisements.AdDurationTracker
import amobi.module.common.advertisements.AdvertsConfig
import amobi.module.common.advertisements.AdvertsInstance
import amobi.module.common.advertisements.AdvertsRequestStatus
import amobi.module.common.configs.CommFigs
import amobi.module.common.utils.DebugLogCustom
import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.views.CommActivity
import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdValue
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.OnPaidEventListener
import com.google.android.gms.ads.rewarded.RewardedAd
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback

class AdvertsInstanceReward(
    listAdsID: Array<String>,
    private val adTag: String = "",
) : AdvertsInstance(adTag) {
    companion object {
        private const val EXPIRED_TIME = (1 * CommFigs.MILLIS_HOUR) - (CommFigs.MILLIS_MINUTE)
    }

    private var mRewardAd: RewardedAd? = null
    private var isShowingAd = false

    init {
        this.listAdsID =
            if (AdvertsConfig.instance.isTestAdsMode) {
                arrayOf(AdvertsConfig.TEST_REWARD_AD_ID)
            } else {
                listAdsID
            }
        adUnitId = this.listAdsID[0]
    }

    val isAdvertsAvailable: Boolean
        get() = mRewardAd != null && isNotExpired

    private val isNotExpired: Boolean
        get() = System.currentTimeMillis() - requestSuccessMillis < EXPIRED_TIME

    fun requestRewardAd(onAdLoadedListener: ((Boolean) -> Unit?)? = null) {
        val advertsConfig = AdvertsConfig.instance
        if (advertsConfig.isHideAd) {
            onAdLoadedListener?.invoke(false)
            return
        }
        if (isAdvertsAvailable || isAdvertsLoading) {
            onAdLoadedListener?.invoke(false)
            return
        }

        requestStatus = AdvertsRequestStatus.REQUESTING
        lastRequestLoadAdMillis = System.currentTimeMillis()

        if (mRewardAd == null) {
            beginLoadAdSeconds = MixedUtils.currentTimeSeconds()
            FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_REWARD, FirebaseAssist.AD_LOAD, beginLoadAdSeconds)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_REWARD, FirebaseAssist.AD_LOAD, beginLoadAdSeconds)
        } else {
            FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_REWARD, FirebaseAssist.AD_RELOAD, beginLoadAdSeconds)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_REWARD, FirebaseAssist.AD_RELOAD, beginLoadAdSeconds)
        }
        val adRequest = AdRequest.Builder().build()

        if (CommFigs.IS_SHOW_TEST_OPTION)
            DebugLogCustom.logd(getAdsName() + " 🟡: REQUEST BEGIN", CommFigs.LOG_TAG_REWARD_AD)

        RewardedAd.load(
            CommApplication.appContext,
            adUnitId!!,
            adRequest,
            object : RewardedAdLoadCallback() {
                override fun onAdLoaded(rewardedAd: RewardedAd) {
                    super.onAdLoaded(rewardedAd)
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🟢: REQUEST SUCCESS", CommFigs.LOG_TAG_REWARD_AD)

                    requestSuccessMillis = System.currentTimeMillis()
                    mRewardAd = rewardedAd
                    rewardedAd.onPaidEventListener =
                        OnPaidEventListener { adValue: AdValue ->
                            DebugLogCustom.logd(" ${getAdsName()} 🟢: onPaidEvent: $adValue", CommFigs.LOG_TAG_REWARD_AD)
                            val mediationAdapter = rewardedAd.responseInfo.mediationAdapterClassName
                            sendLogAdverts(adValue, mediationAdapter)

                            adValueMicros = adValue.valueMicros
                            mediationAdapterClassName = mediationAdapter ?: ""
                            adSourceName = rewardedAd.responseInfo.loadedAdapterResponseInfo?.adSourceName ?: ""
                            checkSendLogDuration()
                        }
                    FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_REWARD, FirebaseAssist.AD_LOADED, beginLoadAdSeconds)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_REWARD, FirebaseAssist.AD_LOADED, beginLoadAdSeconds)

                    admobErrorCode = null
                    requestStatus = AdvertsRequestStatus.RESPONSE_OK
                    onAdLoadedListener?.invoke(true)
                }

                override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                    admobErrorCode = loadAdError.code
                    super.onAdFailedToLoad(loadAdError)
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🔴: REQUEST ERROR " + loadAdError.code + " -> " + loadAdError.message, CommFigs.LOG_TAG_REWARD_AD)

                    mRewardAd = null
                    FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_REWARD, FirebaseAssist.AD_LOAD_FAILED, beginLoadAdSeconds, loadAdError.code)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_REWARD, FirebaseAssist.AD_LOAD_FAILED, beginLoadAdSeconds, loadAdError.code)
                    requestStatus = AdvertsRequestStatus.RESPONSE_ERROR

                    val newAdId = findNextAdsId(adUnitId)
                    if (!newAdId.isNullOrEmpty()) {
                        adUnitId = newAdId
                        requestRewardAd(onAdLoadedListener = onAdLoadedListener)
                    }
                }
            },
        )
    }

    fun showRewardAd(
        activity: CommActivity,
        placement: String? = null,
        onUserEarnedReward: (() -> Unit)? = null,
        onShowAdCompleteListener: (() -> Unit?)? = null,
        onAdDismissedListener: (() -> Unit?)? = null,
    ): Boolean {
        val advertsConfig = AdvertsConfig.instance
        if (advertsConfig.isHideAd) {
            onShowAdCompleteListener?.invoke()
            return false
        }
        if (isShowingAd) {
            if (CommFigs.IS_SHOW_TEST_OPTION)
                DebugLogCustom.logd(getAdsName() + " 🔵: AD ALREADY SHOWING", CommFigs.LOG_TAG_REWARD_AD)
            onShowAdCompleteListener?.invoke()
            return false
        }

        if (!isAdvertsAvailable) {
            if (CommFigs.IS_SHOW_TEST_OPTION)
                DebugLogCustom.logd(getAdsName() + " 🔵: AD WASN'T READY", CommFigs.LOG_TAG_REWARD_AD)
            onShowAdCompleteListener?.invoke()
            FirebaseAssist.instance.logShowAd(FirebaseAssist.AD_REWARD, FirebaseAssist.AD_SHOW_FAILED, admobErrorCode)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logShowSpecificAd(adTag, FirebaseAssist.AD_REWARD, FirebaseAssist.AD_SHOW_FAILED, admobErrorCode)
            requestRewardAd()
            return false
        }

        isShowingAd = true
        resetLogDurationVariables()
        this.placement = placement

        mRewardAd?.fullScreenContentCallback =
            object : FullScreenContentCallback() {
                override fun onAdClicked() {
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🤩: AD CLICKED", CommFigs.LOG_TAG_REWARD_AD)
                    AdDurationTracker.instance.onUserClickedAd()

                    FirebaseAssist.instance.logClickAd(FirebaseAssist.AD_REWARD, beginLoadAdSeconds)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logClickSpecificAd(adTag, FirebaseAssist.AD_REWARD, beginLoadAdSeconds)

                    LocalBroadcastManager
                        .getInstance(CommApplication.appContext)
                        .sendBroadcast(
                            Intent().apply {
                                action = AdvertsInstance.ACTION_AD_CLICKED
                                putExtra(AdvertsInstance.ACTION_AD_CLICKED_AD_ID, adUnitId)
                            },
                        )
                }

                override fun onAdDismissedFullScreenContent() {
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 😔: DISMISSED FULL AD", CommFigs.LOG_TAG_REWARD_AD)

                    adMetrics = AdDurationTracker.instance.onAdDismissedFullScreenContent().copy()
                    CommApplication.fullScreenAdInstance = null
                    checkSendLogDuration()

                    if (!isShowingAd) {
                        if (CommFigs.IS_SHOW_TEST_OPTION)
                            DebugLogCustom.logd(getAdsName() + " 🔴: TRY DISMISSED FULL AD ALREADY FAIL TO SHOW ", CommFigs.LOG_TAG_REWARD_AD)
                        return
                    }

                    mRewardAd?.fullScreenContentCallback = null
                    mRewardAd = null
                    isShowingAd = false

                    onShowAdCompleteListener?.invoke()
                    onAdDismissedListener?.invoke()

                    requestRewardAd()

                    CommApplication.isShowingFullscreenAds = false
                }

                override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🔴: FAILED TO DISPLAY FULL AD: " + adError.code + " -> " + adError.message, CommFigs.LOG_TAG_REWARD_AD)

                    FirebaseAssist.instance.logShowAd(FirebaseAssist.AD_REWARD, FirebaseAssist.AD_SHOW_FAILED)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logShowSpecificAd(adTag, FirebaseAssist.AD_REWARD, FirebaseAssist.AD_SHOW_FAILED)

                    CommApplication.fullScreenAdInstance = null

                    if (!isShowingAd) {
                        if (CommFigs.IS_SHOW_TEST_OPTION)
                            DebugLogCustom.logd(getAdsName() + " 🔴: CALL FAILED FULL AD ALREADY SHOW ", CommFigs.LOG_TAG_REWARD_AD)
                        return
                    }

                    mRewardAd?.fullScreenContentCallback = null
                    mRewardAd = null
                    isShowingAd = false
                    onShowAdCompleteListener?.invoke()

                    requestRewardAd()

                    CommApplication.isShowingFullscreenAds = false
                }

                override fun onAdImpression() {
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🤩: IMPRESSION FULL AD", CommFigs.LOG_TAG_REWARD_AD)
                }

                override fun onAdShowedFullScreenContent() {
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🤩: DISPLAYED FULL AD", CommFigs.LOG_TAG_REWARD_AD)

                    AdDurationTracker.instance.onAdShowedFullScreenContent()
                    CommApplication.fullScreenAdInstance = this@AdvertsInstanceReward

                    FirebaseAssist.instance.logShowAd(FirebaseAssist.AD_REWARD, FirebaseAssist.AD_SHOW_SUCCESS)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logShowSpecificAd(adTag, FirebaseAssist.AD_REWARD, FirebaseAssist.AD_SHOW_SUCCESS)

                    CommApplication.isShowingFullscreenAds = true
                }
            }

        mRewardAd?.show(activity) {
            DebugLogCustom.logd(getAdsName() + " 🤩: REWARD EARNED", CommFigs.LOG_TAG_REWARD_AD)
            onUserEarnedReward?.invoke()
        }

        return true
    }

    // AdMob Track Duration ===============================
    private var adMetrics: AdDurationTracker.AdMetrics? = null
    private var adValueMicros: Long? = null
    private var mediationAdapterClassName: String? = null
    private var adSourceName: String? = null
    private var placement: String? = null

    private fun checkSendLogDuration() {
        val adMetrics = adMetrics ?: return
        val adValueMicros = adValueMicros ?: return
        val mediationAdapterClassName = mediationAdapterClassName ?: return
        val adSourceName = adSourceName ?: return
        sendLogAdDuration(adMetrics, adValueMicros, mediationAdapterClassName, adSourceName, placement)
        resetLogDurationVariables()
    }

    private fun resetLogDurationVariables() {
        adMetrics = null
        adValueMicros = null
        mediationAdapterClassName = null
        adSourceName = null
    }

    fun onActivityPaused() {
        DebugLogCustom.logd(" ${getAdsName()} ON PAUSED", CommFigs.LOG_TAG_REWARD_AD)
        AdDurationTracker.instance.onUserLeavesApp()
    }

    fun onActivityResumed() {
        DebugLogCustom.logd(" ${getAdsName()} ON RESUMED", CommFigs.LOG_TAG_REWARD_AD)
        AdDurationTracker.instance.onUserReturnsToApp()
    }
}
