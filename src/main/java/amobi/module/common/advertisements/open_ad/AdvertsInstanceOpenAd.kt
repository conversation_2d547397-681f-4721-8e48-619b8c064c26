package amobi.module.common.advertisements.open_ad

import amobi.module.common.CommApplication
import amobi.module.common.advertisements.AdDurationTracker
import amobi.module.common.advertisements.AdvertsConfig
import amobi.module.common.advertisements.AdvertsInstance
import amobi.module.common.advertisements.AdvertsRequestStatus
import amobi.module.common.advertisements.native_ad.AdvertsManagerNative.requestNativeAdverts
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.RconfAssist
import amobi.module.common.configs.RconfComm
import amobi.module.common.utils.DebugLogCustom
import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.views.CommActivity
import android.content.Context
import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdValue
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.OnPaidEventListener
import com.google.android.gms.ads.appopen.AppOpenAd
import java.util.Date

internal class AdvertsInstanceOpenAd(
    adId: String,
    isOpenApp: Boolean,
    private val adTag: String = "",
) : AdvertsInstance(adTag) {
    companion object {
        private const val EXPIRED_TIME = (4 * CommFigs.MILLIS_HOUR) - (CommFigs.MILLIS_MINUTE)
    }

    private var appOpenAd: AppOpenAd? = null
    private var isOpenApp = false
    private var isShowingAd = false

    init {
        adUnitId =
            if (AdvertsConfig.instance.isTestAdsMode) {
                AdvertsConfig.TEST_OPEN_AD_ID
            } else {
                adId
            }
        this.isOpenApp = isOpenApp
    }

    val isAdvertsAvailable: Boolean
        get() = appOpenAd != null && isNotExpired

    private val isNotExpired: Boolean
        get() = System.currentTimeMillis() - requestSuccessMillis < EXPIRED_TIME

    fun requestOpenAd(context: Context?) {
        val advertsConfig = AdvertsConfig.instance
        val isEnableOpenAd = if (isOpenApp) true else advertsConfig.isEnableOpenAdsResume
        if (advertsConfig.isHideAd || !isEnableOpenAd) return
        if (isAdvertsAvailable || isAdvertsLoading) return

        lastRequestLoadAdMillis = System.currentTimeMillis()

        requestStatus = AdvertsRequestStatus.REQUESTING

        val request = AdRequest.Builder().build()
        if (appOpenAd == null) {
            beginLoadAdSeconds = MixedUtils.currentTimeSeconds()
            FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_OPEN_AD, FirebaseAssist.AD_LOAD, beginLoadAdSeconds)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_OPEN_AD, FirebaseAssist.AD_LOAD, beginLoadAdSeconds)
        } else {
            FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_OPEN_AD, FirebaseAssist.AD_RELOAD, beginLoadAdSeconds)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_OPEN_AD, FirebaseAssist.AD_RELOAD, beginLoadAdSeconds)
        }

        if (CommFigs.IS_SHOW_TEST_OPTION)
            DebugLogCustom.logd(getAdsName() + " 🟡: REQUEST BEGIN", CommFigs.LOG_TAG_OPEN_AD)

        AppOpenAd.load(
            context!!,
            adUnitId!!,
            request,
            object : AppOpenAd.AppOpenAdLoadCallback() {
                override fun onAdLoaded(adView: AppOpenAd) {
                    super.onAdLoaded(adView)
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🟢: REQUEST SUCCESS", CommFigs.LOG_TAG_OPEN_AD)

                    admobErrorCode = null
                    requestStatus = AdvertsRequestStatus.RESPONSE_OK
                    appOpenAd = adView
                    adView.onPaidEventListener =
                        OnPaidEventListener { adValue: AdValue ->
                            DebugLogCustom.logd(" ${getAdsName()} 🟢: onPaidEvent: $adValue", CommFigs.LOG_TAG_INTER_AD)
                            val mediationAdapter = adView.responseInfo.mediationAdapterClassName
                            sendLogAdverts(adValue, mediationAdapter)

                            adValueMicros = adValue.valueMicros
                            mediationAdapterClassName = mediationAdapter ?: ""
                            adSourceName = adView.responseInfo.loadedAdapterResponseInfo?.adSourceName ?: ""
                            checkSendLogDuration()
                        }
                    FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_OPEN_AD, FirebaseAssist.AD_LOADED, beginLoadAdSeconds)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_OPEN_AD, FirebaseAssist.AD_LOADED, beginLoadAdSeconds)
                    requestSuccessMillis = Date().time
                }

                override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                    admobErrorCode = loadAdError.code
                    super.onAdFailedToLoad(loadAdError)
                    requestStatus = AdvertsRequestStatus.RESPONSE_ERROR
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🔴: REQUEST ERROR " + loadAdError.code + " -> " + loadAdError.message, CommFigs.LOG_TAG_OPEN_AD)

                    FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_OPEN_AD, FirebaseAssist.AD_LOAD_FAILED, beginLoadAdSeconds, loadAdError.code)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_OPEN_AD, FirebaseAssist.AD_LOAD_FAILED, beginLoadAdSeconds, loadAdError.code)

                    val newAdId = findNextAdsId(adUnitId)
                    if (!newAdId.isNullOrEmpty()) {
                        adUnitId = newAdId
                        requestOpenAd(context = context)
                    }
                }
            },
        )
    }

    @JvmOverloads
    fun showAdIfAvailable(
        activity: CommActivity,
        isBypassTimeDelay: Boolean = false,
        placement: String? = null,
        onShowAdCompleteListener: (() -> Unit?)? = null,
    ): Boolean {
        val advertsConfig = AdvertsConfig.instance
        val isEnableOpenAd = if (isOpenApp) advertsConfig.isEnableOpenAdsOpen else advertsConfig.isEnableOpenAdsResume
        if (advertsConfig.isHideAd || !isEnableOpenAd) {
            onShowAdCompleteListener?.invoke()
            return false
        }
        if (isShowingAd) {
            if (CommFigs.IS_SHOW_TEST_OPTION)
                DebugLogCustom.logd(getAdsName() + " 🔵: AD ALREADY SHOWING", CommFigs.LOG_TAG_OPEN_AD)
            onShowAdCompleteListener?.invoke()
            return false
        }

        if (!isAdvertsAvailable) {
            if (CommFigs.IS_SHOW_TEST_OPTION)
                DebugLogCustom.logd(getAdsName() + " 🔵: AD WASN'T READY", CommFigs.LOG_TAG_OPEN_AD)
            onShowAdCompleteListener?.invoke()
            if (!isOpenApp) requestOpenAd(activity)
            return false
        }

        val currentTime = System.currentTimeMillis()
        val isBypassTimeDelayCorrected =
            if (isBypassTimeDelay && !RconfAssist.getBoolean(RconfComm.COMM_DELAY_INTER_ADS_BYPASS_ALLOWED)) {
                false
            } else {
                isBypassTimeDelay
            }

        if (!isBypassTimeDelayCorrected) {
            val oldTime = getLastTimeShowedFullAd()
            if (currentTime - oldTime < RconfAssist.getLong(RconfComm.COMM_DELAY_INTER_ADS) * CommFigs.MILLIS_SECOND) {
                onShowAdCompleteListener?.invoke()
                return false
            }
        }
        setLastTimeShowedFullAd(currentTime)
        isShowingAd = true

        resetLogDurationVariables()
        this.placement = placement
        appOpenAd?.fullScreenContentCallback =
            object : FullScreenContentCallback() {
                override fun onAdClicked() {
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🤩: AD CLICKED", CommFigs.LOG_TAG_INTER_AD)
                    AdDurationTracker.instance.onUserClickedAd()

                    FirebaseAssist.instance.logClickAd(FirebaseAssist.AD_OPEN_AD, beginLoadAdSeconds)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logClickSpecificAd(adTag, FirebaseAssist.AD_OPEN_AD, beginLoadAdSeconds)

                    LocalBroadcastManager
                        .getInstance(CommApplication.appContext)
                        .sendBroadcast(
                            Intent().apply {
                                action = AdvertsInstance.ACTION_AD_CLICKED
                                putExtra(AdvertsInstance.ACTION_AD_CLICKED_AD_ID, adUnitId)
                            },
                        )
                }

                override fun onAdDismissedFullScreenContent() {
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 😔: DISMISSED FULL AD", CommFigs.LOG_TAG_OPEN_AD)
                    setLastTimeShowedFullAd(System.currentTimeMillis())

                    adMetrics = AdDurationTracker.instance.onAdDismissedFullScreenContent().copy()
                    CommApplication.fullScreenAdInstance = null
                    checkSendLogDuration()

                    if (!isShowingAd) {
                        if (CommFigs.IS_SHOW_TEST_OPTION)
                            DebugLogCustom.logd(getAdsName() + " 🔴: TRY DISMISSED FULL AD ALREADY FAIL TO SHOW ", CommFigs.LOG_TAG_INTER_AD)
                        return
                    }
                    appOpenAd?.fullScreenContentCallback = null
                    appOpenAd = null
                    isShowingAd = false

                    onShowAdCompleteListener?.invoke()
                    if (!isOpenApp) requestOpenAd(CommApplication.appContext)

                    CommApplication.isShowingFullscreenAds = false
                }

                override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🔴: FAILED TO DISPLAY FULL AD: " + adError.code + " -> " + adError.message, CommFigs.LOG_TAG_OPEN_AD)

                    FirebaseAssist.instance.logShowAd(FirebaseAssist.AD_OPEN_AD, FirebaseAssist.AD_SHOW_FAILED, admobErrorCode)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logShowSpecificAd(adTag, FirebaseAssist.AD_OPEN_AD, FirebaseAssist.AD_SHOW_FAILED, admobErrorCode)

                    CommApplication.fullScreenAdInstance = null

                    if (!isShowingAd) {
                        if (CommFigs.IS_SHOW_TEST_OPTION)
                            DebugLogCustom.logd(getAdsName() + " 🔴: CALL FAILED FULL AD ALREADY SHOW ", CommFigs.LOG_TAG_OPEN_AD)
                        return
                    }
                    appOpenAd?.fullScreenContentCallback = null
                    appOpenAd = null
                    isShowingAd = false

                    onShowAdCompleteListener?.invoke()
                    if (!isOpenApp) requestOpenAd(CommApplication.appContext)

                    clearLastTimeShowedFullAd()

                    CommApplication.isShowingFullscreenAds = false
                }

                override fun onAdShowedFullScreenContent() {
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🤩: DISPLAYED FULL AD", CommFigs.LOG_TAG_OPEN_AD)

                    AdDurationTracker.instance.onAdShowedFullScreenContent()
                    CommApplication.fullScreenAdInstance = this@AdvertsInstanceOpenAd

                    FirebaseAssist.instance.logShowAd(FirebaseAssist.AD_OPEN_AD, FirebaseAssist.AD_SHOW_SUCCESS)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logShowSpecificAd(adTag, FirebaseAssist.AD_OPEN_AD, FirebaseAssist.AD_SHOW_SUCCESS)

                    CommApplication.isShowingFullscreenAds = true
                }
            }
        appOpenAd?.show(activity)
        return true
    }

    private var adMetrics: AdDurationTracker.AdMetrics? = null
    private var adValueMicros: Long? = null
    private var mediationAdapterClassName: String? = null
    private var adSourceName: String? = null
    private var placement: String? = null

    private fun checkSendLogDuration() {
        val adMetrics = adMetrics ?: return
        val adValueMicros = adValueMicros ?: return
        val mediationAdapterClassName = mediationAdapterClassName ?: return
        val adSourceName = adSourceName ?: return
        sendLogAdDuration(
            adMetrics,
            adValueMicros,
            mediationAdapterClassName,
            adSourceName,
            placement,
        )
        resetLogDurationVariables()
    }

    private fun resetLogDurationVariables() {
        adMetrics = null
        adValueMicros = null
        mediationAdapterClassName = null
        adSourceName = null
    }

    fun onActivityPaused() {
        DebugLogCustom.logd(" ${getAdsName()} ON PAUSED", CommFigs.LOG_TAG_OPEN_AD)
        AdDurationTracker.instance.onUserLeavesApp()
    }

    fun onActivityResumed() {
        DebugLogCustom.logd(" ${getAdsName()} ON RESUMED", CommFigs.LOG_TAG_OPEN_AD)
        AdDurationTracker.instance.onUserReturnsToApp()
    }
}
