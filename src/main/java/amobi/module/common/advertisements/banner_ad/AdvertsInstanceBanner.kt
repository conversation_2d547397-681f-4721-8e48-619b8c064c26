package amobi.module.common.advertisements.banner_ad

import amobi.module.common.CommApplication
import amobi.module.common.advertisements.AdvertsConfig
import amobi.module.common.advertisements.AdvertsInstance
import amobi.module.common.advertisements.AdvertsRequestStatus
import amobi.module.common.configs.CommFigs
import amobi.module.common.utils.DebugLogCustom
import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.views.CommActivity
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.View
import android.view.ViewGroup
import android.view.WindowMetrics
import androidx.annotation.RequiresApi
import androidx.core.view.isNotEmpty
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.ads.mediation.admob.AdMobAdapter
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.AdValue
import com.google.android.gms.ads.AdView
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.OnPaidEventListener
import java.lang.ref.WeakReference
import java.util.UUID

class AdvertsInstanceBanner(
    listAdsID: Array<String>,
    private val adTag: String = "",
) : AdvertsInstance(adTag) {
    companion object {
        private const val EXPIRED_TIME = (1 * CommFigs.MILLIS_HOUR) - (CommFigs.MILLIS_MINUTE)

        fun getBannerAdvertsSize(context: Context): AdSize {
            val displayMetrics = context.resources.displayMetrics
            val widthPixels = displayMetrics.widthPixels.toFloat()
            val density = displayMetrics.density
            val adWidth = (widthPixels / density).toInt()
            return AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(context, adWidth)
        }

        @Suppress("DEPRECATION")
        fun getBannerAdvertsSize(activity: Activity): AdSize {
            val display = activity.windowManager.defaultDisplay
            val outMetrics = DisplayMetrics()
            display.getMetrics(outMetrics)

            val density = outMetrics.density

            val adWidthPixels = outMetrics.widthPixels.toFloat()

            val adWidth = (adWidthPixels / density).toInt()
            return AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(activity, adWidth)
        }
    }

    private var adView: AdView? = null
    private var adSize: AdSize? = null

    private var adsContainer: WeakReference<ViewGroup> = WeakReference(null)
    private var placeHolderContainer: WeakReference<View> = WeakReference(null)
    private var onShowBannerAdCallback: ((Boolean) -> Unit)? = null

    init {
        this.listAdsID =
            if (AdvertsConfig.instance.isTestAdsMode) {
                arrayOf(AdvertsConfig.TEST_BANNER_AD_ID)
            } else {
                listAdsID
            }
        adUnitId = this.listAdsID[0]
    }

    val isAdvertsAvailable: Boolean
        get() = adView != null && isNotExpired

    private val isNotExpired: Boolean
        get() = System.currentTimeMillis() - requestSuccessMillis < EXPIRED_TIME

    fun requestBannerAdverts(
        onAdLoadedListener: (() -> Unit?)? = null,
        activityCollapsible: CommActivity? = null,
        activity: CommActivity? = null,
        adSize: AdSize? = null,
    ) {
        if (AdvertsConfig.instance.isHideAd || !AdvertsConfig.instance.isEnableBannerAds) return
        if (isAdvertsAvailable || isAdvertsLoading) return

        requestStatus = AdvertsRequestStatus.REQUESTING
        lastRequestLoadAdMillis = System.currentTimeMillis()

        if (adView == null) {
            beginLoadAdSeconds = MixedUtils.currentTimeSeconds()
            FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_BANNER, FirebaseAssist.AD_LOAD, beginLoadAdSeconds)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_BANNER, FirebaseAssist.AD_LOAD, beginLoadAdSeconds)
        } else {
            FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_BANNER, FirebaseAssist.AD_RELOAD, beginLoadAdSeconds)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_BANNER, FirebaseAssist.AD_RELOAD, beginLoadAdSeconds)
        }
        val adView = AdView(activityCollapsible ?: CommApplication.appContext)
        this.adView = adView
        adView.adUnitId = adUnitId!!
        adView.visibility = View.GONE

        if (CommFigs.IS_SHOW_TEST_OPTION)
            DebugLogCustom.logd(getAdsName() + " 🟡: REQUEST BEGIN", CommFigs.LOG_TAG_BANNER_AD)

        if (activityCollapsible != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            adView.setAdSize(getCollapsibleAdSize(activityCollapsible))
            // Create an extra parameter that aligns the bottom of the expanded ad to
            // the bottom of the bannerView.
            val extras = Bundle()
            extras.putString("collapsible", "bottom")
            extras.putString("collapsible_request_id", UUID.randomUUID().toString()) // expand once
            val adRequest: AdRequest = AdRequest.Builder().addNetworkExtrasBundle(AdMobAdapter::class.java, extras).build()
            adView.loadAd(adRequest)
        } else {
            val finalAdSize =
                adSize ?: this.adSize ?: if (activity?.isSafe() == true)
                    getBannerAdvertsSize(activity)
                else
                    getBannerAdvertsSize(CommApplication.appContext)

            this.adSize = finalAdSize
            adView.setAdSize(finalAdSize)
            adView.loadAd(AdRequest.Builder().build())
        }
        adView.adListener =
            object : AdListener() {
                override fun onAdClicked() {
                    super.onAdClicked()

                    FirebaseAssist.instance.logClickAd(FirebaseAssist.AD_BANNER, beginLoadAdSeconds)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logClickSpecificAd(adTag, FirebaseAssist.AD_BANNER, beginLoadAdSeconds)

                    LocalBroadcastManager
                        .getInstance(CommApplication.appContext)
                        .sendBroadcast(
                            Intent().apply {
                                action = AdvertsInstance.ACTION_AD_CLICKED
                                putExtra(AdvertsInstance.ACTION_AD_CLICKED_AD_ID, adUnitId)
                            },
                        )
                }

                override fun onAdLoaded() {
                    super.onAdLoaded()
                    requestSuccessMillis = System.currentTimeMillis()
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🟢: REQUEST SUCCESS", CommFigs.LOG_TAG_BANNER_AD)

                    admobErrorCode = null
                    requestStatus = AdvertsRequestStatus.RESPONSE_OK
                    onAdLoadedListener?.invoke()
                    FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_BANNER, FirebaseAssist.AD_LOADED, beginLoadAdSeconds)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_BANNER, FirebaseAssist.AD_LOADED, beginLoadAdSeconds)

                    adsContainer.get()?.let {
                        showBannerAdverts(it, placeHolderContainer.get())
                    }
                    onShowBannerAdCallback?.invoke(true)
                    onShowBannerAdCallback = null
                }

                override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                    admobErrorCode = loadAdError.code
                    super.onAdFailedToLoad(loadAdError)

                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🔴: REQUEST ERROR " + loadAdError.code + " -> " + loadAdError.message, CommFigs.LOG_TAG_BANNER_AD)

                    requestStatus = AdvertsRequestStatus.RESPONSE_ERROR

                    FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_BANNER, FirebaseAssist.AD_LOAD_FAILED, beginLoadAdSeconds, loadAdError.code)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_BANNER, FirebaseAssist.AD_LOAD_FAILED, beginLoadAdSeconds, loadAdError.code)

                    adsContainer.get()?.let {
                        FirebaseAssist.instance.logShowAd(FirebaseAssist.AD_BANNER, FirebaseAssist.AD_SHOW_FAILED, admobErrorCode)
                        if (adTag.isNotEmpty())
                            FirebaseAssist.instance.logShowSpecificAd(adTag, FirebaseAssist.AD_BANNER, FirebaseAssist.AD_SHOW_FAILED, admobErrorCode)
                    }

                    onShowBannerAdCallback?.invoke(false)
                    onShowBannerAdCallback = null



                    val newAdId = findNextAdsId(adUnitId)
                    if (!newAdId.isNullOrEmpty()) {
                        adUnitId = newAdId
                        requestBannerAdverts(
                            onAdLoadedListener = onAdLoadedListener,
                            activityCollapsible = activityCollapsible,
                            activity = activity,
                            adSize = adSize,
                        )
                    }
                }
            }
        adView.onPaidEventListener =
            OnPaidEventListener { adValue: AdValue ->
                val mediationAdapter = adView.responseInfo?.mediationAdapterClassName
                sendLogAdverts(adValue, mediationAdapter)
            }
    }

    fun showBannerAdverts(
        container: ViewGroup,
        placeHolderContainer: View? = null,
        onShowBannerAdCallback: ((Boolean) -> Unit)? = null,
    ) {
        if (AdvertsConfig.instance.isHideAd || !AdvertsConfig.instance.isEnableBannerAds) return
        if (adView == null) {
            adsContainer = WeakReference(container)
            this.placeHolderContainer = WeakReference(placeHolderContainer)
            container.visibility = View.GONE
            placeHolderContainer?.visibility = View.VISIBLE

            this.onShowBannerAdCallback = onShowBannerAdCallback
            requestBannerAdverts()
            return
        }

        val adView = adView ?: return
        try {
            if (container.isNotEmpty()) {
                container.removeAllViews()
            }
            if (adView.parent != null) {
                val adsParent = adView.parent as ViewGroup
                adsParent.minimumHeight = adsParent.height
                adsParent.removeAllViews()
            }
            if (CommFigs.IS_SHOW_TEST_OPTION)
                DebugLogCustom.logd(getAdsName() + " 🔵: DISPLAY", CommFigs.LOG_TAG_BANNER_AD)
            adView.visibility = View.VISIBLE
            container.addView(adView)
            container.visibility = View.VISIBLE
            placeHolderContainer?.visibility = View.GONE
            FirebaseAssist.instance.logShowAd(FirebaseAssist.AD_BANNER, FirebaseAssist.AD_SHOW_SUCCESS)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logShowSpecificAd(adTag, FirebaseAssist.AD_BANNER, FirebaseAssist.AD_SHOW_SUCCESS)
            onShowBannerAdCallback?.invoke(true)
        } catch (e: Exception) {
            e.printStackTrace()
            container.visibility = View.GONE
            placeHolderContainer?.visibility = View.VISIBLE
            FirebaseAssist.instance.logShowAd(FirebaseAssist.AD_BANNER, FirebaseAssist.AD_SHOW_FAILED, admobErrorCode)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logShowSpecificAd(adTag, FirebaseAssist.AD_BANNER, FirebaseAssist.AD_SHOW_FAILED, admobErrorCode)
            onShowBannerAdCallback?.invoke(false)
        }
    }

    @RequiresApi(Build.VERSION_CODES.R)
    private fun getCollapsibleAdSize(activity: CommActivity): AdSize {
        val windowMetrics: WindowMetrics = activity.windowManager.currentWindowMetrics
        val bounds: Rect = windowMetrics.bounds
        val adWidthPixels: Float = bounds.width().toFloat()
        val density: Float = activity.resources.displayMetrics.density
        val adWidth: Int = (adWidthPixels / density).toInt()
        return AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(activity, adWidth)
    }

    fun destroyBannerAd() {
        if (CommFigs.IS_SHOW_TEST_OPTION)
            DebugLogCustom.logd(getAdsName() + " 🔵: DESTROY", CommFigs.LOG_TAG_BANNER_AD)
        adView?.adListener = object : AdListener() {} // prevent memory leak at listener

        adView?.destroy()
        adView = null
    }
}
