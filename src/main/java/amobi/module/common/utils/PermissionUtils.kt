package amobi.module.common.utils

import amobi.module.common.CommApplication
import android.Manifest
import android.app.NotificationManager
import android.content.Context
import android.content.Context.POWER_SERVICE
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.PowerManager
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat

object PermissionUtils {
    fun checkFullScreenIntentPermission(context: Context? = null): Boolean =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            val notificationManager =
                (context ?: CommApplication.appContext).getSystemService(
                    Context.NOTIFICATION_SERVICE,
                ) as NotificationManager
            notificationManager.canUseFullScreenIntent()
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            checkAccess(context, Manifest.permission.USE_FULL_SCREEN_INTENT)
        } else {
            true
        }

    fun checkCameraPermission(context: Context? = null): Boolean = checkAccess(context, Manifest.permission.CAMERA)

    fun checkLocationPermission(context: Context? = null): Boolean {
//        boolean hasAccessFineLocationPermission = checkAccess(context, Manifest.permission.ACCESS_FINE_LOCATION);
        val hasAccessFineLocationPermission = true
        val hasAccessCoarseLocationPermission = checkAccess(context, Manifest.permission.ACCESS_COARSE_LOCATION)
        return hasAccessFineLocationPermission && hasAccessCoarseLocationPermission
    }

    fun checkBackgroundAccessLocationPermission(context: Context? = null): Boolean =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            checkAccess(context, Manifest.permission.ACCESS_BACKGROUND_LOCATION)
        } else {
            checkLocationPermission(context)
        }

    fun checkAccurateAccessLocationPermission(context: Context? = null): Boolean {
        val hasAccessFineLocationPermission = checkAccess(context, Manifest.permission.ACCESS_FINE_LOCATION)
        val hasAccessCoarseLocationPermission = checkAccess(context, Manifest.permission.ACCESS_COARSE_LOCATION)
        return hasAccessFineLocationPermission && hasAccessCoarseLocationPermission
    }

    fun checkStorageAccessPermission(context: Context? = null): Boolean = checkAccess(context, Manifest.permission.READ_EXTERNAL_STORAGE)

    fun checkNotificationPermission(context: Context? = null): Boolean =
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU)
            NotificationManagerCompat.from(context ?: CommApplication.appContext).areNotificationsEnabled()
        else
            checkAccess(context, Manifest.permission.POST_NOTIFICATIONS)

    private fun checkAccess(
        context: Context?,
        string: String,
    ): Boolean {
        val hasAccess = ContextCompat.checkSelfPermission(context ?: CommApplication.appContext, string)
        return hasAccess == PackageManager.PERMISSION_GRANTED
    }
}
