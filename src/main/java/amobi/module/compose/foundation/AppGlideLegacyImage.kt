package amobi.module.compose.foundation

import android.content.res.Configuration
import android.os.Build
import android.view.ViewGroup
import android.widget.ImageView
import androidx.annotation.DrawableRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect
import com.bumptech.glide.Glide
import com.bumptech.glide.integration.compose.Transition
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions


@Composable
fun AppGlideLegacyImage(
    model: Any?,
    modifier: Modifier = Modifier,
    contentDescription: String? = null,
    contentScale: ContentScale = ContentScale.Fit,
    @DrawableRes loading: Int? = null,
    failure: Object? = null,
    transition: Transition.Factory? = null,
) {
    val configuration = LocalConfiguration.current
    val context = LocalContext.current

    var isViewInited by remember { mutableStateOf(false) }

    var imgViewLegacy by remember { mutableStateOf<AppCompatImageView?>(null) }

    var orientation by remember {
        mutableIntStateOf(Configuration.ORIENTATION_PORTRAIT)
    }

    LaunchedEffect(configuration) {
        snapshotFlow { configuration.orientation }
            .collect {
                orientation = it

                imgViewLegacy?.let { imgView ->
                    imgView.invalidate()
                    var glide = Glide.with(context).load(model)
                    if (loading != null)
                        glide = glide.placeholder(loading)
                    if (failure != null)
                        glide = glide.error(failure)
                    if (transition != null)
                        glide = glide.transition(
                            DrawableTransitionOptions.withCrossFade(250)
                        )
                    glide.into(imgView)
                }
            }
    }

    LifecycleEventEffect(event = Lifecycle.Event.ON_START) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            imgViewLegacy?.let { imgView ->
                imgView.invalidate()
                var glide = Glide.with(context).load(model)
                if (loading != null)
                    glide = glide.placeholder(loading)
                if (failure != null)
                    glide = glide.error(failure)
                if (transition != null)
                    glide = glide.transition(
                        DrawableTransitionOptions.withCrossFade(250)
                    )
                glide.into(imgView)
            }
        }
    }

    AndroidView(
        modifier = modifier,
        factory = { ctx ->
            AppCompatImageView(ctx)
                .apply {
                    layoutParams =
                        ViewGroup.LayoutParams(
                            if (contentScale == ContentScale.FillHeight)
                                ViewGroup.LayoutParams.WRAP_CONTENT
                            else
                                ViewGroup.LayoutParams.MATCH_PARENT,

                            if (contentScale == ContentScale.FillWidth)
                                ViewGroup.LayoutParams.WRAP_CONTENT
                            else
                                ViewGroup.LayoutParams.MATCH_PARENT,
                        )

                    scaleType = when (contentScale) {
                        ContentScale.Crop -> ImageView.ScaleType.CENTER_CROP
                        ContentScale.Fit -> ImageView.ScaleType.FIT_CENTER

                        ContentScale.FillWidth, ContentScale.FillHeight -> {
                            adjustViewBounds = true
                            ImageView.ScaleType.FIT_CENTER
                        }

                        ContentScale.FillBounds, ContentScale.FillHeight, ContentScale.FillWidth ->
                            ImageView.ScaleType.FIT_XY

                        ContentScale.Inside -> ImageView.ScaleType.CENTER_INSIDE
                        else -> ImageView.ScaleType.CENTER_INSIDE
                    }
                    contentDescription?.let { this.contentDescription = it }
                }.also { imgViewLegacy = it }
        },
        update = { imgView ->
            if (!isViewInited) {
                var glide = Glide.with(context).load(model)
                if (loading != null)
                    glide = glide.placeholder(loading)
                if (failure != null)
                    glide = glide.error(failure)
                if (transition != null)
                    glide = glide.transition(
                        DrawableTransitionOptions.withCrossFade(250)
                    )
                glide.into(imgView)

                isViewInited = true
            }
        },
    )
}

@Composable
fun AppGlideLegacyImage(
    @DrawableRes resId: Int,
    modifier: Modifier = Modifier,
    contentDescription: String? = null,
    contentScale: ContentScale = ContentScale.Fit,
    @DrawableRes loading: Int? = null,
    failure: Object? = null,
    transition: Transition.Factory? = null,
) {
    AppGlideLegacyImage(
        model = resId,
        modifier = modifier,
        contentDescription = contentDescription,
        contentScale = contentScale,
        loading = loading,
        failure = failure,
        transition = transition
    )
}