import os
import sys
import time

flavor_name = 'Dev'
flavor_build = 'Release'

path = 'app/build/outputs/apk/' + flavor_name + '/release'

testerEmails = '\"<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>\"'

def func_up_version_name():
    # open file build_version.txt (only 1 number example: "24") and get version minor and up it by 1
    #  if not exist create it and write "0"
    if not os.path.exists('build_version.txt'):
        with open('build_version.txt', 'w') as f:
            f.write('1')
            f.close()
    else:
        with open('build_version.txt', 'r') as f:
            version = f.readline()
            f.close()
        with open('build_version.txt', 'w') as f:
            f.write(str(int(version) + 1))
            f.close()




def func_release_note():
    # check file [appdistribution][Product]ReleaseNotes.txt exist or not if not create it and write "No release note"
    if not os.path.exists('build_release_notes.txt'):
        with open('build_release_notes.txt', 'w') as f:
            f.write('No release note')
            f.close()
        # also add this file to .gitignore, if it already ignored, it will not add again
        # check every line in .gitignore contain build_release_notes.txt or not, if not add it
    with open('.gitignore', 'r') as f:
        lines = f.readlines()
        f.close()

        if 'build_release_notes.txt' not in lines and 'build_release_notes.txt\n' not in lines:
            with open('.gitignore', 'a') as f:
                f.write('\nbuild_release_notes.txt')
                f.close()

    # remove line contain [Debug] or [Release]
    with open('build_release_notes.txt', 'r') as f:
        lines = f.readlines()
        f.close()
        with open('build_release_notes.txt', 'w') as f:
            for line in lines:
                if '_Debug_' not in line and '_Release_' not in line:
                    f.write(line)
            f.close()

    # open file release note and insert first line to it [Product] [Release] [Note: 2021-09-09 09:09:09]
    with open('build_release_notes.txt', 'r') as f:
        lines = f.readlines()
        f.close()
        releaseNote = '[' + flavor_name + '_' + flavor_build + '_' + time.strftime('%H:%M:%S') + ']'
        lines.insert(0, releaseNote + '\n')
        with open('build_release_notes.txt', 'w') as f:
            f.writelines(lines)
            f.close()

def func_build():

    command = 'gradlew assemble' + flavor_name + flavor_build + ' appDistributionUpload' + flavor_name + flavor_build
    command += ' --testers=' + testerEmails
    command += ' --releaseNotesFile=build_release_notes.txt'

    if sys.platform == 'win32':
        os.system(command)
    else:
        os.system('chmod +x gradlew')
        command = './' + command
        os.system(command)

def func_open_folder():
    for file in os.listdir(path):
        if file.endswith(".apk"):
            print(os.path.join(path, file))

            if sys.platform == 'win32':
                os.startfile(os.path.realpath(path))
            else:
                os.system('open ' + path)


            sys.exit(0)
            raise SystemExit(0)



if __name__ == "__main__":

    if len(sys.argv) > 1:
        flavor_name = "" + sys.argv[1]
    if len(sys.argv) > 2:
        flavor_build = "" + sys.argv[2]

    # cap first letter of flavor_name
    flavor_name = flavor_name[0].upper() + flavor_name[1:]
    flavor_build = flavor_build[0].upper() + flavor_build[1:]

    path = 'app/build/outputs/apk/' + flavor_name + '/' + flavor_build.lower()

    try:
        for file in os.listdir(path):
            if file.endswith(".apk"):
                os.remove(os.path.join(path, file))
                print('DELETE ' + os.path.join(path, file))
    except:
        print('FOUND NO APK FILE TO DELETE')

    func_up_version_name()
    func_release_note()
    func_build()
    # func_open_folder()

